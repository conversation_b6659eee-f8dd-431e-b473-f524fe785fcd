package com.calorietracker.app.ui.foodentry

import android.os.Parcelable
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.calorietracker.app.data.api.models.FoodAnalysisResult
import com.calorietracker.app.data.api.models.FoodItem
import com.calorietracker.app.data.repository.CalorieRepository
import com.calorietracker.app.ui.camera.FoodAnalysisResultWithImage
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import javax.inject.Inject

@HiltViewModel
class FoodEntryViewModel @Inject constructor(
    private val repository: CalorieRepository
) : ViewModel() {

    private val _foodItems = MutableStateFlow<List<EditableFoodItem>>(emptyList())
    val foodItems: StateFlow<List<EditableFoodItem>> = _foodItems.asStateFlow()

    private val _imagePath = MutableStateFlow<String?>(null)
    val imagePath: StateFlow<String?> = _imagePath.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _saveComplete = MutableStateFlow(false)
    val saveComplete: StateFlow<Boolean> = _saveComplete.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    fun setAnalysisResult(result: FoodAnalysisResultWithImage) {
        _imagePath.value = result.imagePath
        _foodItems.value = result.analysisResult.foods.map { foodItem ->
            EditableFoodItem(
                id = generateTempId(),
                name = foodItem.name,
                servingSize = foodItem.servingSize,
                servingUnit = foodItem.servingUnit,
                calories = foodItem.calories,
                protein = foodItem.protein,
                fat = foodItem.fat,
                carbohydrates = foodItem.carbohydrates,
                fiber = foodItem.fiber,
                sugar = foodItem.sugar,
                sodium = foodItem.sodium
            )
        }
    }

    fun updateFoodItem(updatedFoodItem: EditableFoodItem) {
        val currentItems = _foodItems.value.toMutableList()
        val index = currentItems.indexOfFirst { it.id == updatedFoodItem.id }
        if (index != -1) {
            currentItems[index] = updatedFoodItem
            _foodItems.value = currentItems
        }
    }

    fun removeFoodItem(foodItemId: String) {
        val currentItems = _foodItems.value.toMutableList()
        currentItems.removeAll { it.id == foodItemId }
        _foodItems.value = currentItems
    }

    fun saveFoodEntry() {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null

            try {
                val foodItems = _foodItems.value
                if (foodItems.isEmpty()) {
                    _errorMessage.value = "No food items to save"
                    return@launch
                }

                // Convert back to FoodAnalysisResult
                val analysisResult = FoodAnalysisResult(
                    foods = foodItems.map { editableItem ->
                        FoodItem(
                            name = editableItem.name,
                            servingSize = editableItem.servingSize,
                            servingUnit = editableItem.servingUnit,
                            calories = editableItem.calories,
                            protein = editableItem.protein,
                            fat = editableItem.fat,
                            carbohydrates = editableItem.carbohydrates,
                            fiber = editableItem.fiber,
                            sugar = editableItem.sugar,
                            sodium = editableItem.sodium
                        )
                    }
                )

                repository.saveFoodEntry(analysisResult, _imagePath.value)
                _saveComplete.value = true
            } catch (e: Exception) {
                _errorMessage.value = "Error saving food entry: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    private fun generateTempId(): String {
        return "temp_${System.currentTimeMillis()}_${(0..1000).random()}"
    }

    fun clearError() {
        _errorMessage.value = null
    }
}

@Parcelize
data class EditableFoodItem(
    val id: String,
    val name: String,
    val servingSize: Double,
    val servingUnit: String,
    val calories: Double,
    val protein: Double,
    val fat: Double,
    val carbohydrates: Double,
    val fiber: Double? = null,
    val sugar: Double? = null,
    val sodium: Double? = null
) : Parcelable
