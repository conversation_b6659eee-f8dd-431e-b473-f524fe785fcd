package com.calorietracker.app.data.repository

import android.graphics.Bitmap
import android.util.Base64
import com.calorietracker.app.BuildConfig
import com.calorietracker.app.data.api.GeminiApiService
import com.calorietracker.app.data.api.models.*
import com.calorietracker.app.data.database.dao.FoodEntryDao
import com.calorietracker.app.data.database.dao.NutrientDao
import com.calorietracker.app.data.database.entities.FoodEntry
import com.calorietracker.app.data.database.entities.Nutrient
import com.google.gson.Gson
import kotlinx.coroutines.flow.Flow
import java.io.ByteArrayOutputStream
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CalorieRepository @Inject constructor(
    private val geminiApiService: GeminiApiService,
    private val foodEntryDao: FoodEntryDao,
    private val nutrientDao: NutrientDao,
    private val gson: Gson
) {

    // API Methods
    suspend fun analyzeFoodImage(bitmap: Bitmap): Result<FoodAnalysisResult> {
        return try {
            val base64Image = bitmapToBase64(bitmap)
            val request = createGeminiRequest(base64Image)
            
            val response = geminiApiService.analyzeFoodImage(
                apiKey = BuildConfig.GEMINI_API_KEY,
                request = request
            )
            
            if (response.isSuccessful && response.body() != null) {
                val geminiResponse = response.body()!!
                val analysisResult = parseGeminiResponse(geminiResponse)
                Result.success(analysisResult)
            } else {
                Result.failure(Exception("API call failed: ${response.errorBody()?.string()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun bitmapToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.DEFAULT)
    }

    private fun createGeminiRequest(base64Image: String): GeminiRequest {
        val prompt = """
            Analyze this food image and provide detailed nutritional information for each food item visible. 
            Return the response in this exact JSON format:
            {
              "foods": [
                {
                  "name": "food_name",
                  "serving_size": 100,
                  "serving_unit": "g",
                  "calories": 250,
                  "protein": 12.5,
                  "fat": 8.2,
                  "carbohydrates": 35.4,
                  "fiber": 2.1,
                  "sugar": 5.0,
                  "sodium": 150
                }
              ]
            }
            
            Please estimate realistic portion sizes and provide accurate nutritional values per serving.
            If you cannot identify a food item clearly, skip it. Ensure all numeric values are realistic.
        """.trimIndent()

        return GeminiRequest(
            contents = listOf(
                Content(
                    parts = listOf(
                        Part(text = prompt),
                        Part(
                            inlineData = InlineData(
                                mimeType = "image/jpeg",
                                data = base64Image
                            )
                        )
                    )
                )
            )
        )
    }

    private fun parseGeminiResponse(response: GeminiResponse): FoodAnalysisResult {
        val text = response.candidates.firstOrNull()?.content?.parts?.firstOrNull()?.text
            ?: throw Exception("No response text found")
        
        // Extract JSON from the response text
        val jsonStart = text.indexOf("{")
        val jsonEnd = text.lastIndexOf("}") + 1
        
        if (jsonStart == -1 || jsonEnd <= jsonStart) {
            throw Exception("No valid JSON found in response")
        }
        
        val jsonText = text.substring(jsonStart, jsonEnd)
        return gson.fromJson(jsonText, FoodAnalysisResult::class.java)
    }

    // Database Methods
    suspend fun saveFoodEntry(
        foodAnalysisResult: FoodAnalysisResult,
        imageUri: String? = null
    ): Long {
        val totalCalories = foodAnalysisResult.foods.sumOf { it.calories }
        val totalName = if (foodAnalysisResult.foods.size == 1) {
            foodAnalysisResult.foods.first().name
        } else {
            "Mixed meal (${foodAnalysisResult.foods.size} items)"
        }
        
        val foodEntry = FoodEntry(
            name = totalName,
            servingSize = foodAnalysisResult.foods.sumOf { it.servingSize },
            timestamp = System.currentTimeMillis(),
            imageUri = imageUri,
            totalCalories = totalCalories
        )
        
        val entryId = foodEntryDao.insert(foodEntry)
        
        // Save all nutrients for all food items
        val nutrients = mutableListOf<Nutrient>()
        foodAnalysisResult.foods.forEach { food ->
            nutrients.addAll(
                listOf(
                    Nutrient(foodEntryId = entryId, name = "calories", amount = food.calories, unit = "kcal"),
                    Nutrient(foodEntryId = entryId, name = "protein", amount = food.protein, unit = "g"),
                    Nutrient(foodEntryId = entryId, name = "fat", amount = food.fat, unit = "g"),
                    Nutrient(foodEntryId = entryId, name = "carbohydrates", amount = food.carbohydrates, unit = "g")
                )
            )
            
            food.fiber?.let {
                nutrients.add(Nutrient(foodEntryId = entryId, name = "fiber", amount = it, unit = "g"))
            }
            food.sugar?.let {
                nutrients.add(Nutrient(foodEntryId = entryId, name = "sugar", amount = it, unit = "g"))
            }
            food.sodium?.let {
                nutrients.add(Nutrient(foodEntryId = entryId, name = "sodium", amount = it, unit = "mg"))
            }
        }
        
        nutrientDao.insertAll(nutrients)
        return entryId
    }

    suspend fun updateFoodEntry(foodEntry: FoodEntry) {
        foodEntryDao.update(foodEntry)
    }

    suspend fun deleteFoodEntry(foodEntryId: Long) {
        nutrientDao.deleteByFoodEntryId(foodEntryId)
        foodEntryDao.deleteById(foodEntryId)
    }

    fun getAllFoodEntries(): Flow<List<FoodEntry>> {
        return foodEntryDao.getAllFoodEntries()
    }

    fun getFoodEntriesForDate(date: Long): Flow<List<FoodEntry>> {
        return foodEntryDao.getFoodEntriesForDate(date)
    }

    suspend fun getFoodEntry(id: Long): FoodEntry? {
        return foodEntryDao.getFoodEntry(id)
    }

    suspend fun getNutrientsForFoodEntry(foodEntryId: Long): List<Nutrient> {
        return nutrientDao.getNutrientsForFoodEntry(foodEntryId)
    }

    suspend fun getTotalCaloriesForDate(date: Long): Double {
        return foodEntryDao.getTotalCaloriesForDate(date) ?: 0.0
    }

    suspend fun getTotalNutrientForDate(nutrientName: String, date: Long): Double {
        return nutrientDao.getTotalNutrientForDate(nutrientName, date) ?: 0.0
    }

    fun getFoodEntriesInRange(startTime: Long, endTime: Long): Flow<List<FoodEntry>> {
        return foodEntryDao.getFoodEntriesInRange(startTime, endTime)
    }
}
