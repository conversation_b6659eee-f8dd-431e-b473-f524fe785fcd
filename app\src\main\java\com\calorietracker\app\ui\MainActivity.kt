package com.calorietracker.app.ui

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.calorietracker.app.R
import com.calorietracker.app.databinding.ActivityMainBinding
import com.calorietracker.app.ui.camera.CameraActivity
import com.calorietracker.app.ui.home.HomeViewModel
import com.calorietracker.app.ui.home.FoodEntryAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: HomeViewModel by viewModels()
    private lateinit var foodEntryAdapter: FoodEntryAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupObservers()
        loadTodaysSummary()
    }

    private fun setupUI() {
        // Setup RecyclerView
        foodEntryAdapter = FoodEntryAdapter { foodEntry ->
            // Handle food entry click - navigate to detail view
            // TODO: Implement detail view navigation
        }
        
        binding.recyclerViewFoodEntries.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = foodEntryAdapter
        }

        // Setup FAB to open camera
        binding.fabAddFood.setOnClickListener {
            val intent = Intent(this, CameraActivity::class.java)
            startActivity(intent)
        }
    }

    private fun setupObservers() {
        // Observe today's food entries
        lifecycleScope.launch {
            viewModel.todaysFoodEntries.collect { foodEntries ->
                foodEntryAdapter.submitList(foodEntries)
            }
        }

        // Observe daily summary
        lifecycleScope.launch {
            viewModel.dailySummary.collect { summary ->
                binding.textTotalCalories.text = "%.0f kcal".format(summary.totalCalories)
                binding.textTotalProtein.text = "%.1f g".format(summary.totalProtein)
                binding.textTotalFat.text = "%.1f g".format(summary.totalFat)
                binding.textTotalCarbs.text = "%.1f g".format(summary.totalCarbohydrates)
                
                // Update progress bars
                val targetCalories = 2000.0 // This could be user-configurable
                val calorieProgress = ((summary.totalCalories / targetCalories) * 100).toInt()
                binding.progressCalories.progress = calorieProgress.coerceAtMost(100)
            }
        }

        // Observe loading state
        lifecycleScope.launch {
            viewModel.isLoading.collect { isLoading ->
                binding.progressBar.visibility = if (isLoading) {
                    android.view.View.VISIBLE
                } else {
                    android.view.View.GONE
                }
            }
        }
    }

    private fun loadTodaysSummary() {
        viewModel.loadTodaysData()
    }

    override fun onResume() {
        super.onResume()
        // Refresh data when returning from camera
        loadTodaysSummary()
    }
}
