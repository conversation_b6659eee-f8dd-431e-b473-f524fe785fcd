package com.calorietracker.app.ui.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u0012\u0012\u0004\u0012\u00020\u0002\u0012\b\u0012\u00060\u0003R\u00020\u00000\u0001:\u0001\u0010B\u0019\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0002\u0010\u0007J\u001c\u0010\b\u001a\u00020\u00062\n\u0010\t\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\n\u001a\u00020\u000bH\u0016J\u001c\u0010\f\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000bH\u0016R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/calorietracker/app/ui/home/<USER>", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/calorietracker/app/data/database/entities/FoodEntry;", "Lcom/calorietracker/app/ui/home/<USER>", "onItemClick", "Lkotlin/Function1;", "", "(Lkotlin/jvm/functions/Function1;)V", "onBindViewHolder", "holder", "position", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "FoodEntryViewHolder", "app_debug"})
public final class FoodEntryAdapter extends androidx.recyclerview.widget.ListAdapter<com.calorietracker.app.data.database.entities.FoodEntry, com.calorietracker.app.ui.home.FoodEntryAdapter.FoodEntryViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.calorietracker.app.data.database.entities.FoodEntry, kotlin.Unit> onItemClick = null;
    
    public FoodEntryAdapter(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.calorietracker.app.data.database.entities.FoodEntry, kotlin.Unit> onItemClick) {
        super(null);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.calorietracker.app.ui.home.FoodEntryAdapter.FoodEntryViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.calorietracker.app.ui.home.FoodEntryAdapter.FoodEntryViewHolder holder, int position) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/calorietracker/app/ui/home/<USER>", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "Lcom/calorietracker/app/databinding/ItemFoodEntryBinding;", "(Lcom/calorietracker/app/ui/home/<USER>/calorietracker/app/databinding/ItemFoodEntryBinding;)V", "bind", "", "foodEntry", "Lcom/calorietracker/app/data/database/entities/FoodEntry;", "app_debug"})
    public final class FoodEntryViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final com.calorietracker.app.databinding.ItemFoodEntryBinding binding = null;
        
        public FoodEntryViewHolder(@org.jetbrains.annotations.NotNull()
        com.calorietracker.app.databinding.ItemFoodEntryBinding binding) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.calorietracker.app.data.database.entities.FoodEntry foodEntry) {
        }
    }
}