package com.calorietracker.app.ui.foodentry;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u0000 \u00142\u00020\u0001:\u0001\u0014B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\r\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0016J\b\u0010\u0011\u001a\u00020\fH\u0016J\b\u0010\u0012\u001a\u00020\fH\u0002J\b\u0010\u0013\u001a\u00020\fH\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f0\u000bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/calorietracker/app/ui/foodentry/EditFoodItemDialog;", "Landroidx/fragment/app/DialogFragment;", "()V", "_binding", "Lcom/calorietracker/app/databinding/DialogEditFoodItemBinding;", "binding", "getBinding", "()Lcom/calorietracker/app/databinding/DialogEditFoodItemBinding;", "foodItem", "Lcom/calorietracker/app/ui/foodentry/EditableFoodItem;", "onSave", "Lkotlin/Function1;", "", "onCreateDialog", "Landroid/app/Dialog;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroyView", "saveChanges", "setupViews", "Companion", "app_debug"})
public final class EditFoodItemDialog extends androidx.fragment.app.DialogFragment {
    @org.jetbrains.annotations.Nullable()
    private com.calorietracker.app.databinding.DialogEditFoodItemBinding _binding;
    private com.calorietracker.app.ui.foodentry.EditableFoodItem foodItem;
    private kotlin.jvm.functions.Function1<? super com.calorietracker.app.ui.foodentry.EditableFoodItem, kotlin.Unit> onSave;
    @org.jetbrains.annotations.NotNull()
    public static final com.calorietracker.app.ui.foodentry.EditFoodItemDialog.Companion Companion = null;
    
    public EditFoodItemDialog() {
        super();
    }
    
    private final com.calorietracker.app.databinding.DialogEditFoodItemBinding getBinding() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.app.Dialog onCreateDialog(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void setupViews() {
    }
    
    private final void saveChanges() {
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\"\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\t0\b\u00a8\u0006\n"}, d2 = {"Lcom/calorietracker/app/ui/foodentry/EditFoodItemDialog$Companion;", "", "()V", "newInstance", "Lcom/calorietracker/app/ui/foodentry/EditFoodItemDialog;", "foodItem", "Lcom/calorietracker/app/ui/foodentry/EditableFoodItem;", "onSave", "Lkotlin/Function1;", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.calorietracker.app.ui.foodentry.EditFoodItemDialog newInstance(@org.jetbrains.annotations.NotNull()
        com.calorietracker.app.ui.foodentry.EditableFoodItem foodItem, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function1<? super com.calorietracker.app.ui.foodentry.EditableFoodItem, kotlin.Unit> onSave) {
            return null;
        }
    }
}