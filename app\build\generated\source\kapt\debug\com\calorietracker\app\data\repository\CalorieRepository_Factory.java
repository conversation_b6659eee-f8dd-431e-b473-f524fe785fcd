// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.calorietracker.app.data.repository;

import com.calorietracker.app.data.api.GeminiApiService;
import com.calorietracker.app.data.database.dao.FoodEntryDao;
import com.calorietracker.app.data.database.dao.NutrientDao;
import com.google.gson.Gson;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CalorieRepository_Factory implements Factory<CalorieRepository> {
  private final Provider<GeminiApiService> geminiApiServiceProvider;

  private final Provider<FoodEntryDao> foodEntryDaoProvider;

  private final Provider<NutrientDao> nutrientDaoProvider;

  private final Provider<Gson> gsonProvider;

  public CalorieRepository_Factory(Provider<GeminiApiService> geminiApiServiceProvider,
      Provider<FoodEntryDao> foodEntryDaoProvider, Provider<NutrientDao> nutrientDaoProvider,
      Provider<Gson> gsonProvider) {
    this.geminiApiServiceProvider = geminiApiServiceProvider;
    this.foodEntryDaoProvider = foodEntryDaoProvider;
    this.nutrientDaoProvider = nutrientDaoProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public CalorieRepository get() {
    return newInstance(geminiApiServiceProvider.get(), foodEntryDaoProvider.get(), nutrientDaoProvider.get(), gsonProvider.get());
  }

  public static CalorieRepository_Factory create(
      Provider<GeminiApiService> geminiApiServiceProvider,
      Provider<FoodEntryDao> foodEntryDaoProvider, Provider<NutrientDao> nutrientDaoProvider,
      Provider<Gson> gsonProvider) {
    return new CalorieRepository_Factory(geminiApiServiceProvider, foodEntryDaoProvider, nutrientDaoProvider, gsonProvider);
  }

  public static CalorieRepository newInstance(GeminiApiService geminiApiService,
      FoodEntryDao foodEntryDao, NutrientDao nutrientDao, Gson gson) {
    return new CalorieRepository(geminiApiService, foodEntryDao, nutrientDao, gson);
  }
}
