package com.calorietracker.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import android.content.Context
import com.calorietracker.app.data.database.dao.FoodEntryDao
import com.calorietracker.app.data.database.dao.NutrientDao
import com.calorietracker.app.data.database.entities.FoodEntry
import com.calorietracker.app.data.database.entities.Nutrient

@Database(
    entities = [FoodEntry::class, Nutrient::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun foodEntryDao(): FoodEntryDao
    abstract fun nutrientDao(): NutrientDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "calorie_tracker_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
