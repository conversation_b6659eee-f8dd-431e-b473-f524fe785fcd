  Manifest android  
permission android.Manifest  CAMERA android.Manifest.permission  Application android.app  Dialog android.app  ActivityCameraBinding android.app.Activity  ActivityFoodEntryBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityResultContracts android.app.Activity  Bitmap android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  CameraViewModel android.app.Activity  
ContextCompat android.app.Activity  EditableFoodItem android.app.Activity  ExecutorService android.app.Activity  FoodEntryAdapter android.app.Activity  FoodEntryViewModel android.app.Activity  FoodItemAdapter android.app.Activity  
HomeViewModel android.app.Activity  ImageCapture android.app.Activity  Int android.app.Activity  List android.app.Activity  Manifest android.app.Activity  MutableList android.app.Activity  PackageManager android.app.Activity  REQUIRED_PERMISSIONS android.app.Activity  String android.app.Activity  Toast android.app.Activity  all android.app.Activity  arrayOf android.app.Activity  finish android.app.Activity  getValue android.app.Activity  provideDelegate android.app.Activity  registerForActivityResult android.app.Activity  startCamera android.app.Activity  
viewModels android.app.Activity  Context android.content  Intent android.content  ActivityCameraBinding android.content.Context  ActivityFoodEntryBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityResultContracts android.content.Context  Bitmap android.content.Context  Boolean android.content.Context  Bundle android.content.Context  CameraViewModel android.content.Context  
ContextCompat android.content.Context  EditableFoodItem android.content.Context  ExecutorService android.content.Context  FoodEntryAdapter android.content.Context  FoodEntryViewModel android.content.Context  FoodItemAdapter android.content.Context  
HomeViewModel android.content.Context  ImageCapture android.content.Context  Int android.content.Context  List android.content.Context  Manifest android.content.Context  MutableList android.content.Context  PackageManager android.content.Context  REQUIRED_PERMISSIONS android.content.Context  String android.content.Context  Toast android.content.Context  all android.content.Context  arrayOf android.content.Context  finish android.content.Context  getValue android.content.Context  provideDelegate android.content.Context  registerForActivityResult android.content.Context  startCamera android.content.Context  
viewModels android.content.Context  ActivityCameraBinding android.content.ContextWrapper  ActivityFoodEntryBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  Bitmap android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  CameraViewModel android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  EditableFoodItem android.content.ContextWrapper  ExecutorService android.content.ContextWrapper  FoodEntryAdapter android.content.ContextWrapper  FoodEntryViewModel android.content.ContextWrapper  FoodItemAdapter android.content.ContextWrapper  
HomeViewModel android.content.ContextWrapper  ImageCapture android.content.ContextWrapper  Int android.content.ContextWrapper  List android.content.ContextWrapper  Manifest android.content.ContextWrapper  MutableList android.content.ContextWrapper  PackageManager android.content.ContextWrapper  REQUIRED_PERMISSIONS android.content.ContextWrapper  String android.content.ContextWrapper  Toast android.content.ContextWrapper  all android.content.ContextWrapper  arrayOf android.content.ContextWrapper  finish android.content.ContextWrapper  getValue android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  startCamera android.content.ContextWrapper  
viewModels android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  
BitmapFactory android.graphics  Matrix android.graphics  Bundle 
android.os  
Parcelable 
android.os  Base64 android.util  Log android.util  LayoutInflater android.view  	ViewGroup android.view  ActivityCameraBinding  android.view.ContextThemeWrapper  ActivityFoodEntryBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  Bitmap  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  CameraViewModel  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  EditableFoodItem  android.view.ContextThemeWrapper  ExecutorService  android.view.ContextThemeWrapper  FoodEntryAdapter  android.view.ContextThemeWrapper  FoodEntryViewModel  android.view.ContextThemeWrapper  FoodItemAdapter  android.view.ContextThemeWrapper  
HomeViewModel  android.view.ContextThemeWrapper  ImageCapture  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  MutableList  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  REQUIRED_PERMISSIONS  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  startCamera  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  
viewModels androidx.activity  ActivityCameraBinding #androidx.activity.ComponentActivity  ActivityFoodEntryBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  Bitmap #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  CameraViewModel #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  EditableFoodItem #androidx.activity.ComponentActivity  ExecutorService #androidx.activity.ComponentActivity  FoodEntryAdapter #androidx.activity.ComponentActivity  FoodEntryViewModel #androidx.activity.ComponentActivity  FoodItemAdapter #androidx.activity.ComponentActivity  
HomeViewModel #androidx.activity.ComponentActivity  ImageCapture #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  MutableList #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  REQUIRED_PERMISSIONS #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  arrayOf #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  startCamera #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  AppCompatActivity androidx.appcompat.app  ActivityCameraBinding (androidx.appcompat.app.AppCompatActivity  ActivityFoodEntryBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  Bitmap (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  CameraViewModel (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  EditableFoodItem (androidx.appcompat.app.AppCompatActivity  ExecutorService (androidx.appcompat.app.AppCompatActivity  FoodEntryAdapter (androidx.appcompat.app.AppCompatActivity  FoodEntryViewModel (androidx.appcompat.app.AppCompatActivity  FoodItemAdapter (androidx.appcompat.app.AppCompatActivity  
HomeViewModel (androidx.appcompat.app.AppCompatActivity  ImageCapture (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  Manifest (androidx.appcompat.app.AppCompatActivity  MutableList (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  REQUIRED_PERMISSIONS (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  all (androidx.appcompat.app.AppCompatActivity  arrayOf (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  getValue (androidx.appcompat.app.AppCompatActivity  provideDelegate (androidx.appcompat.app.AppCompatActivity  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  startCamera (androidx.appcompat.app.AppCompatActivity  
viewModels (androidx.appcompat.app.AppCompatActivity  ActivityResultContracts androidx.camera.core  
ContextCompat androidx.camera.core  ImageCapture androidx.camera.core  Manifest androidx.camera.core  PackageManager androidx.camera.core  REQUIRED_PERMISSIONS androidx.camera.core  Toast androidx.camera.core  all androidx.camera.core  arrayOf androidx.camera.core  getValue androidx.camera.core  provideDelegate androidx.camera.core  
viewModels androidx.camera.core  ProcessCameraProvider androidx.camera.lifecycle  ActivityCameraBinding #androidx.core.app.ComponentActivity  ActivityFoodEntryBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  Bitmap #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CameraViewModel #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  EditableFoodItem #androidx.core.app.ComponentActivity  ExecutorService #androidx.core.app.ComponentActivity  FoodEntryAdapter #androidx.core.app.ComponentActivity  FoodEntryViewModel #androidx.core.app.ComponentActivity  FoodItemAdapter #androidx.core.app.ComponentActivity  
HomeViewModel #androidx.core.app.ComponentActivity  ImageCapture #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  MutableList #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  REQUIRED_PERMISSIONS #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  arrayOf #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  startCamera #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  DialogFragment androidx.fragment.app  Bundle $androidx.fragment.app.DialogFragment  Dialog $androidx.fragment.app.DialogFragment  DialogEditFoodItemBinding $androidx.fragment.app.DialogFragment  EditFoodItemDialog $androidx.fragment.app.DialogFragment  EditableFoodItem $androidx.fragment.app.DialogFragment  Unit $androidx.fragment.app.DialogFragment  Bundle androidx.fragment.app.Fragment  Dialog androidx.fragment.app.Fragment  DialogEditFoodItemBinding androidx.fragment.app.Fragment  EditFoodItemDialog androidx.fragment.app.Fragment  EditableFoodItem androidx.fragment.app.Fragment  Unit androidx.fragment.app.Fragment  ActivityCameraBinding &androidx.fragment.app.FragmentActivity  ActivityFoodEntryBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityResultContracts &androidx.fragment.app.FragmentActivity  Bitmap &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  CameraViewModel &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  EditableFoodItem &androidx.fragment.app.FragmentActivity  ExecutorService &androidx.fragment.app.FragmentActivity  FoodEntryAdapter &androidx.fragment.app.FragmentActivity  FoodEntryViewModel &androidx.fragment.app.FragmentActivity  FoodItemAdapter &androidx.fragment.app.FragmentActivity  
HomeViewModel &androidx.fragment.app.FragmentActivity  ImageCapture &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  Manifest &androidx.fragment.app.FragmentActivity  MutableList &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  REQUIRED_PERMISSIONS &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  all &androidx.fragment.app.FragmentActivity  arrayOf &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  getValue &androidx.fragment.app.FragmentActivity  provideDelegate &androidx.fragment.app.FragmentActivity  registerForActivityResult &androidx.fragment.app.FragmentActivity  startCamera &androidx.fragment.app.FragmentActivity  
viewModels &androidx.fragment.app.FragmentActivity  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  Bitmap androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  CalorieRepository androidx.lifecycle.ViewModel  DailySummary androidx.lifecycle.ViewModel  EditableFoodItem androidx.lifecycle.ViewModel  FoodAnalysisResultWithImage androidx.lifecycle.ViewModel  	FoodEntry androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  System androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  
flatMapLatest androidx.lifecycle.ViewModel  flow androidx.lifecycle.ViewModel  
repository androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  Boolean 2androidx.recyclerview.widget.DiffUtil.ItemCallback  EditableFoodItem 2androidx.recyclerview.widget.DiffUtil.ItemCallback  	FoodEntry 2androidx.recyclerview.widget.DiffUtil.ItemCallback  EditableFoodItem (androidx.recyclerview.widget.ListAdapter  	FoodEntry (androidx.recyclerview.widget.ListAdapter  FoodEntryDiffCallback (androidx.recyclerview.widget.ListAdapter  FoodItemDiffCallback (androidx.recyclerview.widget.ListAdapter  Int (androidx.recyclerview.widget.ListAdapter  ItemEditableFoodBinding (androidx.recyclerview.widget.ListAdapter  ItemFoodEntryBinding (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  
ViewHolder )androidx.recyclerview.widget.RecyclerView  EditableFoodItem 1androidx.recyclerview.widget.RecyclerView.Adapter  	FoodEntry 1androidx.recyclerview.widget.RecyclerView.Adapter  FoodEntryDiffCallback 1androidx.recyclerview.widget.RecyclerView.Adapter  FoodItemDiffCallback 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemEditableFoodBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemFoodEntryBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  EditableFoodItem 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	FoodEntry 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemEditableFoodBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemFoodEntryBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  
ForeignKey 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  CASCADE androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  invoke "androidx.room.ForeignKey.Companion  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AppDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  FoodEntryDao androidx.room.RoomDatabase  NutrientDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  Glide com.bumptech.glide  BuildConfig com.calorietracker.app  CalorieTrackerApplication com.calorietracker.app  R com.calorietracker.app  GeminiApiService com.calorietracker.app.data.api  String com.calorietracker.app.data.api  Body 0com.calorietracker.app.data.api.GeminiApiService  
GeminiRequest 0com.calorietracker.app.data.api.GeminiApiService  GeminiResponse 0com.calorietracker.app.data.api.GeminiApiService  POST 0com.calorietracker.app.data.api.GeminiApiService  Query 0com.calorietracker.app.data.api.GeminiApiService  Response 0com.calorietracker.app.data.api.GeminiApiService  String 0com.calorietracker.app.data.api.GeminiApiService  	Candidate &com.calorietracker.app.data.api.models  Content &com.calorietracker.app.data.api.models  Double &com.calorietracker.app.data.api.models  FoodAnalysisResult &com.calorietracker.app.data.api.models  FoodItem &com.calorietracker.app.data.api.models  
GeminiRequest &com.calorietracker.app.data.api.models  GeminiResponse &com.calorietracker.app.data.api.models  
InlineData &com.calorietracker.app.data.api.models  List &com.calorietracker.app.data.api.models  Part &com.calorietracker.app.data.api.models  ResponseContent &com.calorietracker.app.data.api.models  ResponsePart &com.calorietracker.app.data.api.models  Result &com.calorietracker.app.data.api.models  String &com.calorietracker.app.data.api.models  ResponseContent 0com.calorietracker.app.data.api.models.Candidate  SerializedName 0com.calorietracker.app.data.api.models.Candidate  String 0com.calorietracker.app.data.api.models.Candidate  List .com.calorietracker.app.data.api.models.Content  Part .com.calorietracker.app.data.api.models.Content  SerializedName .com.calorietracker.app.data.api.models.Content  FoodItem 9com.calorietracker.app.data.api.models.FoodAnalysisResult  List 9com.calorietracker.app.data.api.models.FoodAnalysisResult  SerializedName 9com.calorietracker.app.data.api.models.FoodAnalysisResult  Double /com.calorietracker.app.data.api.models.FoodItem  SerializedName /com.calorietracker.app.data.api.models.FoodItem  String /com.calorietracker.app.data.api.models.FoodItem  Content 4com.calorietracker.app.data.api.models.GeminiRequest  List 4com.calorietracker.app.data.api.models.GeminiRequest  SerializedName 4com.calorietracker.app.data.api.models.GeminiRequest  	Candidate 5com.calorietracker.app.data.api.models.GeminiResponse  List 5com.calorietracker.app.data.api.models.GeminiResponse  SerializedName 5com.calorietracker.app.data.api.models.GeminiResponse  SerializedName 1com.calorietracker.app.data.api.models.InlineData  String 1com.calorietracker.app.data.api.models.InlineData  
InlineData +com.calorietracker.app.data.api.models.Part  SerializedName +com.calorietracker.app.data.api.models.Part  String +com.calorietracker.app.data.api.models.Part  List 6com.calorietracker.app.data.api.models.ResponseContent  ResponsePart 6com.calorietracker.app.data.api.models.ResponseContent  SerializedName 6com.calorietracker.app.data.api.models.ResponseContent  String 6com.calorietracker.app.data.api.models.ResponseContent  SerializedName 3com.calorietracker.app.data.api.models.ResponsePart  String 3com.calorietracker.app.data.api.models.ResponsePart  AppDatabase $com.calorietracker.app.data.database  	FoodEntry $com.calorietracker.app.data.database  Nutrient $com.calorietracker.app.data.database  Volatile $com.calorietracker.app.data.database  AppDatabase 0com.calorietracker.app.data.database.AppDatabase  Context 0com.calorietracker.app.data.database.AppDatabase  FoodEntryDao 0com.calorietracker.app.data.database.AppDatabase  NutrientDao 0com.calorietracker.app.data.database.AppDatabase  Volatile 0com.calorietracker.app.data.database.AppDatabase  AppDatabase :com.calorietracker.app.data.database.AppDatabase.Companion  Context :com.calorietracker.app.data.database.AppDatabase.Companion  FoodEntryDao :com.calorietracker.app.data.database.AppDatabase.Companion  NutrientDao :com.calorietracker.app.data.database.AppDatabase.Companion  Volatile :com.calorietracker.app.data.database.AppDatabase.Companion  Dao (com.calorietracker.app.data.database.dao  Delete (com.calorietracker.app.data.database.dao  Double (com.calorietracker.app.data.database.dao  FoodEntryDao (com.calorietracker.app.data.database.dao  Insert (com.calorietracker.app.data.database.dao  List (com.calorietracker.app.data.database.dao  Long (com.calorietracker.app.data.database.dao  NutrientDao (com.calorietracker.app.data.database.dao  OnConflictStrategy (com.calorietracker.app.data.database.dao  Query (com.calorietracker.app.data.database.dao  String (com.calorietracker.app.data.database.dao  Update (com.calorietracker.app.data.database.dao  Delete 5com.calorietracker.app.data.database.dao.FoodEntryDao  Double 5com.calorietracker.app.data.database.dao.FoodEntryDao  Flow 5com.calorietracker.app.data.database.dao.FoodEntryDao  	FoodEntry 5com.calorietracker.app.data.database.dao.FoodEntryDao  Insert 5com.calorietracker.app.data.database.dao.FoodEntryDao  List 5com.calorietracker.app.data.database.dao.FoodEntryDao  Long 5com.calorietracker.app.data.database.dao.FoodEntryDao  Query 5com.calorietracker.app.data.database.dao.FoodEntryDao  Update 5com.calorietracker.app.data.database.dao.FoodEntryDao  Delete 4com.calorietracker.app.data.database.dao.NutrientDao  Double 4com.calorietracker.app.data.database.dao.NutrientDao  Flow 4com.calorietracker.app.data.database.dao.NutrientDao  Insert 4com.calorietracker.app.data.database.dao.NutrientDao  List 4com.calorietracker.app.data.database.dao.NutrientDao  Long 4com.calorietracker.app.data.database.dao.NutrientDao  Nutrient 4com.calorietracker.app.data.database.dao.NutrientDao  OnConflictStrategy 4com.calorietracker.app.data.database.dao.NutrientDao  Query 4com.calorietracker.app.data.database.dao.NutrientDao  String 4com.calorietracker.app.data.database.dao.NutrientDao  Update 4com.calorietracker.app.data.database.dao.NutrientDao  Double -com.calorietracker.app.data.database.entities  	FoodEntry -com.calorietracker.app.data.database.entities  Long -com.calorietracker.app.data.database.entities  Nutrient -com.calorietracker.app.data.database.entities  String -com.calorietracker.app.data.database.entities  Double 7com.calorietracker.app.data.database.entities.FoodEntry  Long 7com.calorietracker.app.data.database.entities.FoodEntry  
PrimaryKey 7com.calorietracker.app.data.database.entities.FoodEntry  String 7com.calorietracker.app.data.database.entities.FoodEntry  Double 6com.calorietracker.app.data.database.entities.Nutrient  Long 6com.calorietracker.app.data.database.entities.Nutrient  
PrimaryKey 6com.calorietracker.app.data.database.entities.Nutrient  String 6com.calorietracker.app.data.database.entities.Nutrient  CalorieRepository &com.calorietracker.app.data.repository  Double &com.calorietracker.app.data.repository  FoodAnalysisResult &com.calorietracker.app.data.repository  
GeminiRequest &com.calorietracker.app.data.repository  GeminiResponse &com.calorietracker.app.data.repository  List &com.calorietracker.app.data.repository  Long &com.calorietracker.app.data.repository  Result &com.calorietracker.app.data.repository  String &com.calorietracker.app.data.repository  Bitmap 8com.calorietracker.app.data.repository.CalorieRepository  Double 8com.calorietracker.app.data.repository.CalorieRepository  Flow 8com.calorietracker.app.data.repository.CalorieRepository  FoodAnalysisResult 8com.calorietracker.app.data.repository.CalorieRepository  	FoodEntry 8com.calorietracker.app.data.repository.CalorieRepository  FoodEntryDao 8com.calorietracker.app.data.repository.CalorieRepository  GeminiApiService 8com.calorietracker.app.data.repository.CalorieRepository  
GeminiRequest 8com.calorietracker.app.data.repository.CalorieRepository  GeminiResponse 8com.calorietracker.app.data.repository.CalorieRepository  Gson 8com.calorietracker.app.data.repository.CalorieRepository  Inject 8com.calorietracker.app.data.repository.CalorieRepository  List 8com.calorietracker.app.data.repository.CalorieRepository  Long 8com.calorietracker.app.data.repository.CalorieRepository  Nutrient 8com.calorietracker.app.data.repository.CalorieRepository  NutrientDao 8com.calorietracker.app.data.repository.CalorieRepository  Result 8com.calorietracker.app.data.repository.CalorieRepository  String 8com.calorietracker.app.data.repository.CalorieRepository  getFoodEntriesForDate 8com.calorietracker.app.data.repository.CalorieRepository  getTotalCaloriesForDate 8com.calorietracker.app.data.repository.CalorieRepository  getTotalNutrientForDate 8com.calorietracker.app.data.repository.CalorieRepository  ActivityCameraBinding "com.calorietracker.app.databinding  ActivityFoodEntryBinding "com.calorietracker.app.databinding  ActivityMainBinding "com.calorietracker.app.databinding  DialogEditFoodItemBinding "com.calorietracker.app.databinding  ItemEditableFoodBinding "com.calorietracker.app.databinding  ItemFoodEntryBinding "com.calorietracker.app.databinding  getROOT :com.calorietracker.app.databinding.ItemEditableFoodBinding  getRoot :com.calorietracker.app.databinding.ItemEditableFoodBinding  root :com.calorietracker.app.databinding.ItemEditableFoodBinding  setRoot :com.calorietracker.app.databinding.ItemEditableFoodBinding  getROOT 7com.calorietracker.app.databinding.ItemFoodEntryBinding  getRoot 7com.calorietracker.app.databinding.ItemFoodEntryBinding  root 7com.calorietracker.app.databinding.ItemFoodEntryBinding  setRoot 7com.calorietracker.app.databinding.ItemFoodEntryBinding  DatabaseModule com.calorietracker.app.di  
NetworkModule com.calorietracker.app.di  SingletonComponent com.calorietracker.app.di  AppDatabase (com.calorietracker.app.di.DatabaseModule  ApplicationContext (com.calorietracker.app.di.DatabaseModule  Context (com.calorietracker.app.di.DatabaseModule  FoodEntryDao (com.calorietracker.app.di.DatabaseModule  NutrientDao (com.calorietracker.app.di.DatabaseModule  Provides (com.calorietracker.app.di.DatabaseModule  	Singleton (com.calorietracker.app.di.DatabaseModule  GeminiApiService 'com.calorietracker.app.di.NetworkModule  Gson 'com.calorietracker.app.di.NetworkModule  OkHttpClient 'com.calorietracker.app.di.NetworkModule  Provides 'com.calorietracker.app.di.NetworkModule  Retrofit 'com.calorietracker.app.di.NetworkModule  	Singleton 'com.calorietracker.app.di.NetworkModule  MainActivity com.calorietracker.app.ui  getValue com.calorietracker.app.ui  provideDelegate com.calorietracker.app.ui  
viewModels com.calorietracker.app.ui  ActivityMainBinding &com.calorietracker.app.ui.MainActivity  Bundle &com.calorietracker.app.ui.MainActivity  FoodEntryAdapter &com.calorietracker.app.ui.MainActivity  
HomeViewModel &com.calorietracker.app.ui.MainActivity  getGETValue &com.calorietracker.app.ui.MainActivity  getGetValue &com.calorietracker.app.ui.MainActivity  getPROVIDEDelegate &com.calorietracker.app.ui.MainActivity  getProvideDelegate &com.calorietracker.app.ui.MainActivity  
getVIEWModels &com.calorietracker.app.ui.MainActivity  getValue &com.calorietracker.app.ui.MainActivity  
getViewModels &com.calorietracker.app.ui.MainActivity  provideDelegate &com.calorietracker.app.ui.MainActivity  
viewModels &com.calorietracker.app.ui.MainActivity  ActivityResultContracts  com.calorietracker.app.ui.camera  Boolean  com.calorietracker.app.ui.camera  CameraActivity  com.calorietracker.app.ui.camera  CameraViewModel  com.calorietracker.app.ui.camera  
ContextCompat  com.calorietracker.app.ui.camera  FoodAnalysisResultWithImage  com.calorietracker.app.ui.camera  ImageCapture  com.calorietracker.app.ui.camera  Int  com.calorietracker.app.ui.camera  Manifest  com.calorietracker.app.ui.camera  MutableList  com.calorietracker.app.ui.camera  MutableStateFlow  com.calorietracker.app.ui.camera  PackageManager  com.calorietracker.app.ui.camera  REQUIRED_PERMISSIONS  com.calorietracker.app.ui.camera  String  com.calorietracker.app.ui.camera  Toast  com.calorietracker.app.ui.camera  all  com.calorietracker.app.ui.camera  arrayOf  com.calorietracker.app.ui.camera  asStateFlow  com.calorietracker.app.ui.camera  getValue  com.calorietracker.app.ui.camera  provideDelegate  com.calorietracker.app.ui.camera  
viewModels  com.calorietracker.app.ui.camera  ActivityCameraBinding /com.calorietracker.app.ui.camera.CameraActivity  ActivityResultContracts /com.calorietracker.app.ui.camera.CameraActivity  Bitmap /com.calorietracker.app.ui.camera.CameraActivity  Boolean /com.calorietracker.app.ui.camera.CameraActivity  Bundle /com.calorietracker.app.ui.camera.CameraActivity  CameraViewModel /com.calorietracker.app.ui.camera.CameraActivity  
ContextCompat /com.calorietracker.app.ui.camera.CameraActivity  ExecutorService /com.calorietracker.app.ui.camera.CameraActivity  ImageCapture /com.calorietracker.app.ui.camera.CameraActivity  Int /com.calorietracker.app.ui.camera.CameraActivity  Manifest /com.calorietracker.app.ui.camera.CameraActivity  MutableList /com.calorietracker.app.ui.camera.CameraActivity  PackageManager /com.calorietracker.app.ui.camera.CameraActivity  REQUIRED_PERMISSIONS /com.calorietracker.app.ui.camera.CameraActivity  String /com.calorietracker.app.ui.camera.CameraActivity  Toast /com.calorietracker.app.ui.camera.CameraActivity  all /com.calorietracker.app.ui.camera.CameraActivity  arrayOf /com.calorietracker.app.ui.camera.CameraActivity  baseContext /com.calorietracker.app.ui.camera.CameraActivity  finish /com.calorietracker.app.ui.camera.CameraActivity  getALL /com.calorietracker.app.ui.camera.CameraActivity  getAll /com.calorietracker.app.ui.camera.CameraActivity  getBASEContext /com.calorietracker.app.ui.camera.CameraActivity  getBaseContext /com.calorietracker.app.ui.camera.CameraActivity  getGETValue /com.calorietracker.app.ui.camera.CameraActivity  getGetValue /com.calorietracker.app.ui.camera.CameraActivity  getPROVIDEDelegate /com.calorietracker.app.ui.camera.CameraActivity  getProvideDelegate /com.calorietracker.app.ui.camera.CameraActivity  
getVIEWModels /com.calorietracker.app.ui.camera.CameraActivity  getValue /com.calorietracker.app.ui.camera.CameraActivity  
getViewModels /com.calorietracker.app.ui.camera.CameraActivity  provideDelegate /com.calorietracker.app.ui.camera.CameraActivity  registerForActivityResult /com.calorietracker.app.ui.camera.CameraActivity  setBaseContext /com.calorietracker.app.ui.camera.CameraActivity  startCamera /com.calorietracker.app.ui.camera.CameraActivity  
viewModels /com.calorietracker.app.ui.camera.CameraActivity  ActivityCameraBinding 9com.calorietracker.app.ui.camera.CameraActivity.Companion  ActivityResultContracts 9com.calorietracker.app.ui.camera.CameraActivity.Companion  Bitmap 9com.calorietracker.app.ui.camera.CameraActivity.Companion  Boolean 9com.calorietracker.app.ui.camera.CameraActivity.Companion  Bundle 9com.calorietracker.app.ui.camera.CameraActivity.Companion  CameraViewModel 9com.calorietracker.app.ui.camera.CameraActivity.Companion  
ContextCompat 9com.calorietracker.app.ui.camera.CameraActivity.Companion  ExecutorService 9com.calorietracker.app.ui.camera.CameraActivity.Companion  ImageCapture 9com.calorietracker.app.ui.camera.CameraActivity.Companion  Int 9com.calorietracker.app.ui.camera.CameraActivity.Companion  Manifest 9com.calorietracker.app.ui.camera.CameraActivity.Companion  MutableList 9com.calorietracker.app.ui.camera.CameraActivity.Companion  PackageManager 9com.calorietracker.app.ui.camera.CameraActivity.Companion  REQUIRED_PERMISSIONS 9com.calorietracker.app.ui.camera.CameraActivity.Companion  String 9com.calorietracker.app.ui.camera.CameraActivity.Companion  Toast 9com.calorietracker.app.ui.camera.CameraActivity.Companion  all 9com.calorietracker.app.ui.camera.CameraActivity.Companion  arrayOf 9com.calorietracker.app.ui.camera.CameraActivity.Companion  getALL 9com.calorietracker.app.ui.camera.CameraActivity.Companion  
getARRAYOf 9com.calorietracker.app.ui.camera.CameraActivity.Companion  getAll 9com.calorietracker.app.ui.camera.CameraActivity.Companion  
getArrayOf 9com.calorietracker.app.ui.camera.CameraActivity.Companion  getGETValue 9com.calorietracker.app.ui.camera.CameraActivity.Companion  getGetValue 9com.calorietracker.app.ui.camera.CameraActivity.Companion  getPROVIDEDelegate 9com.calorietracker.app.ui.camera.CameraActivity.Companion  getProvideDelegate 9com.calorietracker.app.ui.camera.CameraActivity.Companion  getValue 9com.calorietracker.app.ui.camera.CameraActivity.Companion  provideDelegate 9com.calorietracker.app.ui.camera.CameraActivity.Companion  
viewModels 9com.calorietracker.app.ui.camera.CameraActivity.Companion  Bitmap 0com.calorietracker.app.ui.camera.CameraViewModel  Boolean 0com.calorietracker.app.ui.camera.CameraViewModel  CalorieRepository 0com.calorietracker.app.ui.camera.CameraViewModel  FoodAnalysisResultWithImage 0com.calorietracker.app.ui.camera.CameraViewModel  Inject 0com.calorietracker.app.ui.camera.CameraViewModel  MutableStateFlow 0com.calorietracker.app.ui.camera.CameraViewModel  	StateFlow 0com.calorietracker.app.ui.camera.CameraViewModel  String 0com.calorietracker.app.ui.camera.CameraViewModel  _analysisResult 0com.calorietracker.app.ui.camera.CameraViewModel  
_errorMessage 0com.calorietracker.app.ui.camera.CameraViewModel  
_isLoading 0com.calorietracker.app.ui.camera.CameraViewModel  asStateFlow 0com.calorietracker.app.ui.camera.CameraViewModel  getASStateFlow 0com.calorietracker.app.ui.camera.CameraViewModel  getAsStateFlow 0com.calorietracker.app.ui.camera.CameraViewModel  FoodAnalysisResult <com.calorietracker.app.ui.camera.FoodAnalysisResultWithImage  String <com.calorietracker.app.ui.camera.FoodAnalysisResultWithImage  Boolean #com.calorietracker.app.ui.foodentry  Double #com.calorietracker.app.ui.foodentry  EditFoodItemDialog #com.calorietracker.app.ui.foodentry  EditableFoodItem #com.calorietracker.app.ui.foodentry  FoodEntryActivity #com.calorietracker.app.ui.foodentry  FoodEntryViewModel #com.calorietracker.app.ui.foodentry  FoodItemAdapter #com.calorietracker.app.ui.foodentry  FoodItemDiffCallback #com.calorietracker.app.ui.foodentry  Int #com.calorietracker.app.ui.foodentry  List #com.calorietracker.app.ui.foodentry  MutableStateFlow #com.calorietracker.app.ui.foodentry  String #com.calorietracker.app.ui.foodentry  Unit #com.calorietracker.app.ui.foodentry  asStateFlow #com.calorietracker.app.ui.foodentry  	emptyList #com.calorietracker.app.ui.foodentry  getValue #com.calorietracker.app.ui.foodentry  provideDelegate #com.calorietracker.app.ui.foodentry  
viewModels #com.calorietracker.app.ui.foodentry  Bundle 6com.calorietracker.app.ui.foodentry.EditFoodItemDialog  Dialog 6com.calorietracker.app.ui.foodentry.EditFoodItemDialog  DialogEditFoodItemBinding 6com.calorietracker.app.ui.foodentry.EditFoodItemDialog  EditFoodItemDialog 6com.calorietracker.app.ui.foodentry.EditFoodItemDialog  EditableFoodItem 6com.calorietracker.app.ui.foodentry.EditFoodItemDialog  Unit 6com.calorietracker.app.ui.foodentry.EditFoodItemDialog  _binding 6com.calorietracker.app.ui.foodentry.EditFoodItemDialog  Bundle @com.calorietracker.app.ui.foodentry.EditFoodItemDialog.Companion  Dialog @com.calorietracker.app.ui.foodentry.EditFoodItemDialog.Companion  DialogEditFoodItemBinding @com.calorietracker.app.ui.foodentry.EditFoodItemDialog.Companion  EditFoodItemDialog @com.calorietracker.app.ui.foodentry.EditFoodItemDialog.Companion  EditableFoodItem @com.calorietracker.app.ui.foodentry.EditFoodItemDialog.Companion  Unit @com.calorietracker.app.ui.foodentry.EditFoodItemDialog.Companion  Double 4com.calorietracker.app.ui.foodentry.EditableFoodItem  String 4com.calorietracker.app.ui.foodentry.EditableFoodItem  ActivityFoodEntryBinding 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  Bundle 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  EditableFoodItem 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  FoodEntryViewModel 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  FoodItemAdapter 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  List 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  getGETValue 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  getGetValue 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  getPROVIDEDelegate 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  getProvideDelegate 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  
getVIEWModels 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  getValue 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  
getViewModels 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  provideDelegate 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  
viewModels 5com.calorietracker.app.ui.foodentry.FoodEntryActivity  Boolean 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  CalorieRepository 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  EditableFoodItem 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  FoodAnalysisResultWithImage 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  Inject 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  List 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  MutableStateFlow 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  	StateFlow 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  String 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  
_errorMessage 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  
_foodItems 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  
_imagePath 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  
_isLoading 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  
_saveComplete 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  asStateFlow 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  	emptyList 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  getASStateFlow 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  getAsStateFlow 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  getEMPTYList 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  getEmptyList 6com.calorietracker.app.ui.foodentry.FoodEntryViewModel  EditableFoodItem 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  FoodItemDiffCallback 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  FoodItemViewHolder 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  Int 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  ItemEditableFoodBinding 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  RecyclerView 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  Unit 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  	ViewGroup 3com.calorietracker.app.ui.foodentry.FoodItemAdapter  EditableFoodItem Fcom.calorietracker.app.ui.foodentry.FoodItemAdapter.FoodItemViewHolder  ItemEditableFoodBinding Fcom.calorietracker.app.ui.foodentry.FoodItemAdapter.FoodItemViewHolder  Boolean 8com.calorietracker.app.ui.foodentry.FoodItemDiffCallback  EditableFoodItem 8com.calorietracker.app.ui.foodentry.FoodItemDiffCallback  Boolean com.calorietracker.app.ui.home  DailySummary com.calorietracker.app.ui.home  Double com.calorietracker.app.ui.home  FoodEntryAdapter com.calorietracker.app.ui.home  FoodEntryDiffCallback com.calorietracker.app.ui.home  
HomeViewModel com.calorietracker.app.ui.home  Int com.calorietracker.app.ui.home  List com.calorietracker.app.ui.home  Long com.calorietracker.app.ui.home  MutableStateFlow com.calorietracker.app.ui.home  SharingStarted com.calorietracker.app.ui.home  	StateFlow com.calorietracker.app.ui.home  System com.calorietracker.app.ui.home  Unit com.calorietracker.app.ui.home  asStateFlow com.calorietracker.app.ui.home  	emptyList com.calorietracker.app.ui.home  
flatMapLatest com.calorietracker.app.ui.home  flow com.calorietracker.app.ui.home  
repository com.calorietracker.app.ui.home  stateIn com.calorietracker.app.ui.home  viewModelScope com.calorietracker.app.ui.home  Double +com.calorietracker.app.ui.home.DailySummary  	FoodEntry /com.calorietracker.app.ui.home.FoodEntryAdapter  FoodEntryDiffCallback /com.calorietracker.app.ui.home.FoodEntryAdapter  FoodEntryViewHolder /com.calorietracker.app.ui.home.FoodEntryAdapter  Int /com.calorietracker.app.ui.home.FoodEntryAdapter  ItemFoodEntryBinding /com.calorietracker.app.ui.home.FoodEntryAdapter  RecyclerView /com.calorietracker.app.ui.home.FoodEntryAdapter  Unit /com.calorietracker.app.ui.home.FoodEntryAdapter  	ViewGroup /com.calorietracker.app.ui.home.FoodEntryAdapter  	FoodEntry Ccom.calorietracker.app.ui.home.FoodEntryAdapter.FoodEntryViewHolder  ItemFoodEntryBinding Ccom.calorietracker.app.ui.home.FoodEntryAdapter.FoodEntryViewHolder  Boolean 4com.calorietracker.app.ui.home.FoodEntryDiffCallback  	FoodEntry 4com.calorietracker.app.ui.home.FoodEntryDiffCallback  Boolean ,com.calorietracker.app.ui.home.HomeViewModel  CalorieRepository ,com.calorietracker.app.ui.home.HomeViewModel  DailySummary ,com.calorietracker.app.ui.home.HomeViewModel  	FoodEntry ,com.calorietracker.app.ui.home.HomeViewModel  Inject ,com.calorietracker.app.ui.home.HomeViewModel  List ,com.calorietracker.app.ui.home.HomeViewModel  Long ,com.calorietracker.app.ui.home.HomeViewModel  MutableStateFlow ,com.calorietracker.app.ui.home.HomeViewModel  SharingStarted ,com.calorietracker.app.ui.home.HomeViewModel  	StateFlow ,com.calorietracker.app.ui.home.HomeViewModel  System ,com.calorietracker.app.ui.home.HomeViewModel  
_isLoading ,com.calorietracker.app.ui.home.HomeViewModel  
_selectedDate ,com.calorietracker.app.ui.home.HomeViewModel  asStateFlow ,com.calorietracker.app.ui.home.HomeViewModel  	emptyList ,com.calorietracker.app.ui.home.HomeViewModel  
flatMapLatest ,com.calorietracker.app.ui.home.HomeViewModel  flow ,com.calorietracker.app.ui.home.HomeViewModel  getASStateFlow ,com.calorietracker.app.ui.home.HomeViewModel  getAsStateFlow ,com.calorietracker.app.ui.home.HomeViewModel  getEMPTYList ,com.calorietracker.app.ui.home.HomeViewModel  getEmptyList ,com.calorietracker.app.ui.home.HomeViewModel  getFLATMapLatest ,com.calorietracker.app.ui.home.HomeViewModel  getFLOW ,com.calorietracker.app.ui.home.HomeViewModel  getFlatMapLatest ,com.calorietracker.app.ui.home.HomeViewModel  getFlow ,com.calorietracker.app.ui.home.HomeViewModel  
getSTATEIn ,com.calorietracker.app.ui.home.HomeViewModel  
getStateIn ,com.calorietracker.app.ui.home.HomeViewModel  getVIEWModelScope ,com.calorietracker.app.ui.home.HomeViewModel  getViewModelScope ,com.calorietracker.app.ui.home.HomeViewModel  
repository ,com.calorietracker.app.ui.home.HomeViewModel  selectedDate ,com.calorietracker.app.ui.home.HomeViewModel  stateIn ,com.calorietracker.app.ui.home.HomeViewModel  viewModelScope ,com.calorietracker.app.ui.home.HomeViewModel  MaterialCardView  com.google.android.material.card  MaterialAlertDialogBuilder "com.google.android.material.dialog  Gson com.google.gson  GsonBuilder com.google.gson  SerializedName com.google.gson.annotations  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  ByteArrayOutputStream java.io  File java.io  ActivityResultContracts 	java.lang  
ContextCompat 	java.lang  DailySummary 	java.lang  	FoodEntry 	java.lang  Manifest 	java.lang  MutableStateFlow 	java.lang  Nutrient 	java.lang  OnConflictStrategy 	java.lang  PackageManager 	java.lang  REQUIRED_PERMISSIONS 	java.lang  SharingStarted 	java.lang  SingletonComponent 	java.lang  System 	java.lang  Toast 	java.lang  all 	java.lang  arrayOf 	java.lang  asStateFlow 	java.lang  	emptyList 	java.lang  
flatMapLatest 	java.lang  flow 	java.lang  getValue 	java.lang  provideDelegate 	java.lang  
repository 	java.lang  stateIn 	java.lang  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  ActivityResultContracts 	java.util  
ContextCompat 	java.util  DailySummary 	java.util  ImageCapture 	java.util  Manifest 	java.util  MutableStateFlow 	java.util  PackageManager 	java.util  REQUIRED_PERMISSIONS 	java.util  SharingStarted 	java.util  	StateFlow 	java.util  System 	java.util  Toast 	java.util  all 	java.util  arrayOf 	java.util  asStateFlow 	java.util  	emptyList 	java.util  
flatMapLatest 	java.util  flow 	java.util  getValue 	java.util  provideDelegate 	java.util  
repository 	java.util  stateIn 	java.util  viewModelScope 	java.util  
viewModels 	java.util  ExecutorService java.util.concurrent  	Executors java.util.concurrent  TimeUnit java.util.concurrent  Inject javax.inject  	Singleton javax.inject  ActivityResultContracts kotlin  Array kotlin  Boolean kotlin  
ContextCompat kotlin  DailySummary kotlin  Double kotlin  	FoodEntry kotlin  	Function1 kotlin  Int kotlin  Lazy kotlin  Long kotlin  Manifest kotlin  MutableStateFlow kotlin  Nothing kotlin  Nutrient kotlin  OnConflictStrategy kotlin  PackageManager kotlin  REQUIRED_PERMISSIONS kotlin  Result kotlin  SharingStarted kotlin  SingletonComponent kotlin  String kotlin  System kotlin  Toast kotlin  Unit kotlin  Volatile kotlin  all kotlin  arrayOf kotlin  asStateFlow kotlin  	emptyList kotlin  
flatMapLatest kotlin  flow kotlin  getValue kotlin  provideDelegate kotlin  
repository kotlin  stateIn kotlin  getALL kotlin.Array  getAll kotlin.Array  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  ActivityResultContracts kotlin.annotation  
ContextCompat kotlin.annotation  DailySummary kotlin.annotation  	FoodEntry kotlin.annotation  Manifest kotlin.annotation  MutableStateFlow kotlin.annotation  Nutrient kotlin.annotation  OnConflictStrategy kotlin.annotation  PackageManager kotlin.annotation  REQUIRED_PERMISSIONS kotlin.annotation  Result kotlin.annotation  SharingStarted kotlin.annotation  SingletonComponent kotlin.annotation  System kotlin.annotation  Toast kotlin.annotation  Volatile kotlin.annotation  all kotlin.annotation  arrayOf kotlin.annotation  asStateFlow kotlin.annotation  	emptyList kotlin.annotation  
flatMapLatest kotlin.annotation  flow kotlin.annotation  getValue kotlin.annotation  provideDelegate kotlin.annotation  
repository kotlin.annotation  stateIn kotlin.annotation  ActivityResultContracts kotlin.collections  
ContextCompat kotlin.collections  DailySummary kotlin.collections  	FoodEntry kotlin.collections  List kotlin.collections  Manifest kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  Nutrient kotlin.collections  OnConflictStrategy kotlin.collections  PackageManager kotlin.collections  REQUIRED_PERMISSIONS kotlin.collections  Result kotlin.collections  SharingStarted kotlin.collections  SingletonComponent kotlin.collections  System kotlin.collections  Toast kotlin.collections  Volatile kotlin.collections  all kotlin.collections  arrayOf kotlin.collections  asStateFlow kotlin.collections  	emptyList kotlin.collections  
flatMapLatest kotlin.collections  flow kotlin.collections  getValue kotlin.collections  provideDelegate kotlin.collections  
repository kotlin.collections  stateIn kotlin.collections  ActivityResultContracts kotlin.comparisons  
ContextCompat kotlin.comparisons  DailySummary kotlin.comparisons  	FoodEntry kotlin.comparisons  Manifest kotlin.comparisons  MutableStateFlow kotlin.comparisons  Nutrient kotlin.comparisons  OnConflictStrategy kotlin.comparisons  PackageManager kotlin.comparisons  REQUIRED_PERMISSIONS kotlin.comparisons  Result kotlin.comparisons  SharingStarted kotlin.comparisons  SingletonComponent kotlin.comparisons  System kotlin.comparisons  Toast kotlin.comparisons  Volatile kotlin.comparisons  all kotlin.comparisons  arrayOf kotlin.comparisons  asStateFlow kotlin.comparisons  	emptyList kotlin.comparisons  
flatMapLatest kotlin.comparisons  flow kotlin.comparisons  getValue kotlin.comparisons  provideDelegate kotlin.comparisons  
repository kotlin.comparisons  stateIn kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ActivityResultContracts 	kotlin.io  
ContextCompat 	kotlin.io  DailySummary 	kotlin.io  	FoodEntry 	kotlin.io  Manifest 	kotlin.io  MutableStateFlow 	kotlin.io  Nutrient 	kotlin.io  OnConflictStrategy 	kotlin.io  PackageManager 	kotlin.io  REQUIRED_PERMISSIONS 	kotlin.io  Result 	kotlin.io  SharingStarted 	kotlin.io  SingletonComponent 	kotlin.io  System 	kotlin.io  Toast 	kotlin.io  Volatile 	kotlin.io  all 	kotlin.io  arrayOf 	kotlin.io  asStateFlow 	kotlin.io  	emptyList 	kotlin.io  
flatMapLatest 	kotlin.io  flow 	kotlin.io  getValue 	kotlin.io  provideDelegate 	kotlin.io  
repository 	kotlin.io  stateIn 	kotlin.io  ActivityResultContracts 
kotlin.jvm  
ContextCompat 
kotlin.jvm  DailySummary 
kotlin.jvm  	FoodEntry 
kotlin.jvm  Manifest 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  Nutrient 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  PackageManager 
kotlin.jvm  REQUIRED_PERMISSIONS 
kotlin.jvm  Result 
kotlin.jvm  SharingStarted 
kotlin.jvm  SingletonComponent 
kotlin.jvm  System 
kotlin.jvm  Toast 
kotlin.jvm  Volatile 
kotlin.jvm  all 
kotlin.jvm  arrayOf 
kotlin.jvm  asStateFlow 
kotlin.jvm  	emptyList 
kotlin.jvm  
flatMapLatest 
kotlin.jvm  flow 
kotlin.jvm  getValue 
kotlin.jvm  provideDelegate 
kotlin.jvm  
repository 
kotlin.jvm  stateIn 
kotlin.jvm  ActivityResultContracts 
kotlin.ranges  
ContextCompat 
kotlin.ranges  DailySummary 
kotlin.ranges  	FoodEntry 
kotlin.ranges  Manifest 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  Nutrient 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  PackageManager 
kotlin.ranges  REQUIRED_PERMISSIONS 
kotlin.ranges  Result 
kotlin.ranges  SharingStarted 
kotlin.ranges  SingletonComponent 
kotlin.ranges  System 
kotlin.ranges  Toast 
kotlin.ranges  Volatile 
kotlin.ranges  all 
kotlin.ranges  arrayOf 
kotlin.ranges  asStateFlow 
kotlin.ranges  	emptyList 
kotlin.ranges  
flatMapLatest 
kotlin.ranges  flow 
kotlin.ranges  getValue 
kotlin.ranges  provideDelegate 
kotlin.ranges  
repository 
kotlin.ranges  stateIn 
kotlin.ranges  KClass kotlin.reflect  ActivityResultContracts kotlin.sequences  
ContextCompat kotlin.sequences  DailySummary kotlin.sequences  	FoodEntry kotlin.sequences  Manifest kotlin.sequences  MutableStateFlow kotlin.sequences  Nutrient kotlin.sequences  OnConflictStrategy kotlin.sequences  PackageManager kotlin.sequences  REQUIRED_PERMISSIONS kotlin.sequences  Result kotlin.sequences  SharingStarted kotlin.sequences  SingletonComponent kotlin.sequences  System kotlin.sequences  Toast kotlin.sequences  Volatile kotlin.sequences  all kotlin.sequences  arrayOf kotlin.sequences  asStateFlow kotlin.sequences  	emptyList kotlin.sequences  
flatMapLatest kotlin.sequences  flow kotlin.sequences  getValue kotlin.sequences  provideDelegate kotlin.sequences  
repository kotlin.sequences  stateIn kotlin.sequences  ActivityResultContracts kotlin.text  
ContextCompat kotlin.text  DailySummary kotlin.text  	FoodEntry kotlin.text  Manifest kotlin.text  MutableStateFlow kotlin.text  Nutrient kotlin.text  OnConflictStrategy kotlin.text  PackageManager kotlin.text  REQUIRED_PERMISSIONS kotlin.text  Result kotlin.text  SharingStarted kotlin.text  SingletonComponent kotlin.text  System kotlin.text  Toast kotlin.text  Volatile kotlin.text  all kotlin.text  arrayOf kotlin.text  asStateFlow kotlin.text  	emptyList kotlin.text  
flatMapLatest kotlin.text  flow kotlin.text  getValue kotlin.text  provideDelegate kotlin.text  
repository kotlin.text  stateIn kotlin.text  CoroutineScope kotlinx.coroutines  launch kotlinx.coroutines  DailySummary kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  System kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  
flatMapLatest kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  
repository kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  DailySummary %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  
getREPOSITORY %kotlinx.coroutines.flow.FlowCollector  
getRepository %kotlinx.coroutines.flow.FlowCollector  
repository %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  
flatMapLatest !kotlinx.coroutines.flow.StateFlow  getFLATMapLatest !kotlinx.coroutines.flow.StateFlow  getFlatMapLatest !kotlinx.coroutines.flow.StateFlow  	Parcelize kotlinx.parcelize  OkHttpClient okhttp3  HttpLoggingInterceptor okhttp3.logging  EasyPermissions pub.devrel.easypermissions  PermissionCallbacks *pub.devrel.easypermissions.EasyPermissions  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  Body retrofit2.http  POST retrofit2.http  Query retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    