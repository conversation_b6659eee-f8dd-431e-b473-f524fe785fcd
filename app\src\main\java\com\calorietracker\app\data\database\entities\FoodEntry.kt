package com.calorietracker.app.data.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "food_entries")
data class FoodEntry(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val servingSize: Double,
    val timestamp: Long,
    val imageUri: String? = null, // Store image path for reference
    val totalCalories: Double = 0.0
)
