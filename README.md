# Calorie Tracker App

An Android application that uses AI-powered food recognition to track nutritional information. The app integrates with Google's Gemini API to analyze food photos and automatically log nutritional data.

## Features

- 📸 **Photo-based Food Recognition**: Capture photos of meals using the built-in camera
- 🤖 **AI-Powered Analysis**: Uses Gemini API to identify food items and extract nutritional information
- 📊 **Nutritional Tracking**: Tracks calories, protein, fat, carbohydrates, and other nutrients
- ✏️ **Manual Editing**: Review and edit nutritional information before saving
- 📈 **Daily Summaries**: View daily nutritional intake with progress indicators
- 💾 **Offline Storage**: All data is stored locally using Room database
- 🎨 **Modern UI**: Clean, intuitive interface built with Material Design 3

## Technical Architecture

### Built With
- **Kotlin** - Primary programming language
- **Android Jetpack** - Modern Android development components
- **MVVM Architecture** - Clean separation of concerns
- **Hilt** - Dependency injection
- **Room** - Local database storage
- **Retrofit** - HTTP client for API calls
- **CameraX** - Camera functionality
- **Material Design 3** - UI components and theming

### API Integration
- **Gemini Pro Vision API** - Google's multimodal AI for food recognition
- **Real-time Analysis** - Processes food images and returns structured nutritional data

## Prerequisites

- Android Studio Arctic Fox or later
- Android SDK 24 (Android 7.0) or higher
- Valid Gemini API key (included in the project)

## Setup Instructions

### 1. Clone and Import
```bash
git clone <repository-url>
cd CalorieTrackerApp
```

### 2. Open in Android Studio
1. Open Android Studio
2. Select "Open an existing Android Studio project"
3. Navigate to the `CalorieTrackerApp` folder and select it

### 3. API Configuration
The Gemini API key is already configured in the project:
- **API Key**: `AIzaSyAs0vPoI2hA-oBKiLYYC2qVnKSZSzAR5XE`
- **Configuration**: Located in `app/build.gradle` buildConfigField

### 4. Build and Run
1. Ensure you have an Android device connected or emulator running
2. Click the "Run" button in Android Studio
3. Grant camera permissions when prompted

## Project Structure

```
app/
├── src/main/java/com/calorietracker/app/
│   ├── data/                          # Data layer
│   │   ├── api/                       # Gemini API integration
│   │   ├── database/                  # Room database
│   │   └── repository/                # Data repository
│   ├── di/                            # Dependency injection modules
│   ├── ui/                            # User interface layer
│   │   ├── camera/                    # Camera functionality
│   │   ├── foodentry/                 # Food entry management
│   │   └── home/                      # Main dashboard
│   └── CalorieTrackerApplication.kt   # Application class
├── src/main/res/                      # Resources
│   ├── layout/                        # XML layouts
│   ├── values/                        # Colors, strings, themes
│   └── drawable/                      # Icons and graphics
└── build.gradle                       # App-level build configuration
```

## Key Components

### Database Schema
- **FoodEntry**: Stores meal information and timestamps
- **Nutrient**: Stores detailed nutritional data for each food item

### API Integration
- **GeminiApiService**: Retrofit interface for API calls
- **CalorieRepository**: Handles data operations and API integration
- **Image Processing**: Optimizes photos for efficient API submission

### User Interface
- **MainActivity**: Dashboard with daily summary and food entries
- **CameraActivity**: Camera interface for capturing food photos
- **FoodEntryActivity**: Review and edit nutritional information

## Usage

### Adding a Food Entry
1. Tap the camera FAB on the main screen
2. Point camera at your food and tap to capture
3. Wait for AI analysis (usually 2-5 seconds)
4. Review detected food items and nutritional information
5. Edit any values if needed
6. Tap "Save Entry" to log the meal

### Viewing Daily Progress
- Daily calorie and nutrient totals are displayed on the main screen
- Progress bars show intake relative to daily goals
- Recent meals are listed below the summary

### Editing Food Items
- Tap any food item in the review screen to edit
- Modify serving sizes, nutritional values, or food names
- Changes are automatically reflected in totals

## Permissions

The app requires the following permissions:
- **CAMERA** - To capture food photos
- **INTERNET** - To communicate with Gemini API
- **ACCESS_NETWORK_STATE** - To check network connectivity

## Performance Considerations

### Image Optimization
- Photos are automatically resized to 800x600 pixels for API efficiency
- JPEG compression (80% quality) reduces upload time and API costs

### Database Performance
- Room database with proper indexing for fast queries
- Efficient queries for daily summaries and date-based filtering

### API Usage
- Images are compressed to minimize API costs
- Structured prompts ensure consistent response format
- Error handling with user-friendly messages

## Known Limitations

1. **Food Recognition Accuracy**: AI accuracy depends on image quality and lighting
2. **Internet Requirement**: Food analysis requires internet connectivity
3. **API Costs**: Gemini API usage may incur costs (free tier available)
4. **Portion Estimation**: AI estimates may need manual adjustment

## Future Enhancements

- [ ] Barcode scanning for packaged foods
- [ ] Meal planning and recommendations
- [ ] Export data to health apps
- [ ] Multi-language support
- [ ] Voice input for quick logging
- [ ] Social features and meal sharing

## Troubleshooting

### Common Issues

**Camera Permission Denied**
- Solution: Go to Settings > Apps > Calorie Tracker > Permissions and enable Camera

**API Analysis Failed**
- Check internet connection
- Verify API key is valid
- Try taking a clearer photo with better lighting

**App Crashes on Startup**
- Clear app data: Settings > Apps > Calorie Tracker > Storage > Clear Data
- Restart the app

**Food Not Detected**
- Ensure good lighting when taking photos
- Try different angles or closer shots
- Make sure food items are clearly visible

## Support

For technical issues or questions:
1. Check the troubleshooting section above
2. Review the code documentation
3. Open an issue in the project repository

## License

This project is developed for educational and demonstration purposes. The Gemini API key provided is for testing only.

---

**Author**: MiniMax Agent  
**Last Updated**: June 21, 2025
