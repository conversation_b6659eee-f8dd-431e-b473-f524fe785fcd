<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.calorietracker.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="213" endOffset="53"/></Target><Target id="@+id/text_total_calories" view="TextView"><Expressions/><location startLine="60" startOffset="28" endLine="67" endOffset="74"/></Target><Target id="@+id/progress_calories" view="ProgressBar"><Expressions/><location startLine="71" startOffset="24" endLine="78" endOffset="50"/></Target><Target id="@+id/text_total_protein" view="TextView"><Expressions/><location startLine="103" startOffset="28" endLine="109" endOffset="58"/></Target><Target id="@+id/text_total_fat" view="TextView"><Expressions/><location startLine="127" startOffset="28" endLine="133" endOffset="58"/></Target><Target id="@+id/text_total_carbs" view="TextView"><Expressions/><location startLine="151" startOffset="28" endLine="157" endOffset="58"/></Target><Target id="@+id/recycler_view_food_entries" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="181" startOffset="16" endLine="186" endOffset="62"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="195" startOffset="4" endLine="200" endOffset="35"/></Target><Target id="@+id/fab_add_food" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="203" startOffset="4" endLine="211" endOffset="41"/></Target></Targets></Layout>