package com.calorietracker.app.data.database.dao

import androidx.room.*
import com.calorietracker.app.data.database.entities.FoodEntry
import kotlinx.coroutines.flow.Flow

@Dao
interface FoodEntryDao {
    
    @Insert
    suspend fun insert(foodEntry: FoodEntry): Long

    @Query("SELECT * FROM food_entries WHERE id = :id")
    suspend fun getFoodEntry(id: Long): FoodEntry?

    @Query("SELECT * FROM food_entries ORDER BY timestamp DESC")
    fun getAllFoodEntries(): Flow<List<FoodEntry>>
    
    @Query("SELECT * FROM food_entries WHERE DATE(timestamp/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch') ORDER BY timestamp DESC")
    fun getFoodEntriesForDate(date: Long): Flow<List<FoodEntry>>
    
    @Query("SELECT SUM(totalCalories) FROM food_entries WHERE DATE(timestamp/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch')")
    suspend fun getTotalCaloriesForDate(date: Long): Double?
    
    @Query("SELECT * FROM food_entries WHERE timestamp >= :startTime AND timestamp <= :endTime ORDER BY timestamp DESC")
    fun getFoodEntriesInRange(startTime: Long, endTime: Long): Flow<List<FoodEntry>>

    @Update
    suspend fun update(foodEntry: FoodEntry)

    @Delete
    suspend fun delete(foodEntry: FoodEntry)
    
    @Query("DELETE FROM food_entries WHERE id = :id")
    suspend fun deleteById(id: Long)
}
