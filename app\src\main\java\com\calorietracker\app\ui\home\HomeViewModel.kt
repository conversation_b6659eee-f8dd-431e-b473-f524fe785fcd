package com.calorietracker.app.ui.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.calorietracker.app.data.database.entities.FoodEntry
import com.calorietracker.app.data.repository.CalorieRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val repository: CalorieRepository
) : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _selectedDate = MutableStateFlow(System.currentTimeMillis())
    val selectedDate: StateFlow<Long> = _selectedDate.asStateFlow()

    val todaysFoodEntries: StateFlow<List<FoodEntry>> = selectedDate
        .flatMapLatest { date ->
            repository.getFoodEntriesForDate(date)
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val dailySummary: StateFlow<DailySummary> = selectedDate
        .flatMapLatest { date ->
            flow {
                val totalCalories = repository.getTotalCaloriesForDate(date)
                val totalProtein = repository.getTotalNutrientForDate("protein", date)
                val totalFat = repository.getTotalNutrientForDate("fat", date)
                val totalCarbs = repository.getTotalNutrientForDate("carbohydrates", date)
                
                emit(
                    DailySummary(
                        totalCalories = totalCalories,
                        totalProtein = totalProtein,
                        totalFat = totalFat,
                        totalCarbohydrates = totalCarbs
                    )
                )
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = DailySummary()
        )

    fun loadTodaysData() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // Data is automatically loaded through StateFlow observers
                _selectedDate.value = getTodayTimestamp()
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun selectDate(date: Long) {
        _selectedDate.value = date
    }

    fun deleteFoodEntry(foodEntryId: Long) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                repository.deleteFoodEntry(foodEntryId)
            } finally {
                _isLoading.value = false
            }
        }
    }

    private fun getTodayTimestamp(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
}

data class DailySummary(
    val totalCalories: Double = 0.0,
    val totalProtein: Double = 0.0,
    val totalFat: Double = 0.0,
    val totalCarbohydrates: Double = 0.0
)
