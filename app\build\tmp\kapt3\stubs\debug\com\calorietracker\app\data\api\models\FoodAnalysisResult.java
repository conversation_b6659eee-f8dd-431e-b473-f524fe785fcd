package com.calorietracker.app.data.api.models;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0013\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0002\u0010\u0005J\u000f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0019\u0010\t\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\t\u0010\n\u001a\u00020\u000bH\u00d6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u000bH\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\u0019\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u000bH\u00d6\u0001R\u001c\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0018"}, d2 = {"Lcom/calorietracker/app/data/api/models/FoodAnalysisResult;", "Landroid/os/Parcelable;", "foods", "", "Lcom/calorietracker/app/data/api/models/FoodItem;", "(Ljava/util/List;)V", "getFoods", "()Ljava/util/List;", "component1", "copy", "describeContents", "", "equals", "", "other", "", "hashCode", "toString", "", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class FoodAnalysisResult implements android.os.Parcelable {
    @com.google.gson.annotations.SerializedName(value = "foods")
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.calorietracker.app.data.api.models.FoodItem> foods = null;
    
    public FoodAnalysisResult(@org.jetbrains.annotations.NotNull()
    java.util.List<com.calorietracker.app.data.api.models.FoodItem> foods) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.calorietracker.app.data.api.models.FoodItem> getFoods() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.calorietracker.app.data.api.models.FoodItem> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.calorietracker.app.data.api.models.FoodAnalysisResult copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.calorietracker.app.data.api.models.FoodItem> foods) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
}