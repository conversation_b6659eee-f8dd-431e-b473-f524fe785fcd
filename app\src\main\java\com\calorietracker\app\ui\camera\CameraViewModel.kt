package com.calorietracker.app.ui.camera

import android.graphics.Bitmap
import android.os.Parcelable
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.calorietracker.app.data.api.models.FoodAnalysisResult
import com.calorietracker.app.data.repository.CalorieRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import javax.inject.Inject

@HiltViewModel
class CameraViewModel @Inject constructor(
    private val repository: CalorieRepository
) : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _analysisResult = MutableStateFlow<FoodAnalysisResultWithImage?>(null)
    val analysisResult: StateFlow<FoodAnalysisResultWithImage?> = _analysisResult.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    fun analyzeImage(bitmap: Bitmap, imagePath: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null

            try {
                val result = repository.analyzeFoodImage(bitmap)
                
                if (result.isSuccess) {
                    val analysisResult = result.getOrNull()
                    if (analysisResult != null && analysisResult.foods.isNotEmpty()) {
                        _analysisResult.value = FoodAnalysisResultWithImage(
                            analysisResult = analysisResult,
                            imagePath = imagePath
                        )
                    } else {
                        _errorMessage.value = "No food items detected in the image. Please try again with a clearer photo."
                    }
                } else {
                    val error = result.exceptionOrNull()
                    _errorMessage.value = "Analysis failed: ${error?.message ?: "Unknown error"}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Error analyzing image: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun clearError() {
        _errorMessage.value = null
    }

    fun clearResult() {
        _analysisResult.value = null
    }
}

@Parcelize
data class FoodAnalysisResultWithImage(
    val analysisResult: FoodAnalysisResult,
    val imagePath: String
) : Parcelable
