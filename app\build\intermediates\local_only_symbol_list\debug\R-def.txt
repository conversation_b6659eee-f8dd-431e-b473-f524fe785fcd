R_DEF: Internal format may change without notice
local
color accent_color
color black
color carbs_color
color card_background
color error_color
color fat_color
color overlay_background
color primary_color
color primary_color_dark
color protein_color
color purple_200
color purple_500
color purple_700
color success_color
color teal_200
color teal_700
color warning_color
color white
drawable circle_background
drawable ic_arrow_back
drawable ic_camera
drawable ic_edit
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable rounded_background
id button_back
id button_cancel
id button_edit
id button_save
id edit_text_calories
id edit_text_carbohydrates
id edit_text_fat
id edit_text_fiber
id edit_text_food_name
id edit_text_protein
id edit_text_serving_size
id edit_text_serving_unit
id edit_text_sodium
id edit_text_sugar
id fab_add_food
id image_capture_button
id image_view_food
id progress_bar
id progress_calories
id recycler_view_food_entries
id recycler_view_food_items
id text_calories
id text_food_name
id text_macros
id text_serving_size
id text_time
id text_total_calories
id text_total_carbs
id text_total_fat
id text_total_protein
id viewFinder
layout activity_camera
layout activity_food_entry
layout activity_main
layout dialog_edit_food_item
layout item_editable_food
layout item_food_entry
mipmap ic_launcher
mipmap ic_launcher_round
string add_food
string analysis_failed
string analyzing_food
string app_name
string back
string breakfast
string calories
string camera_instructions
string camera_permission_required
string cancel
string carbs
string delete
string detected_food_items
string dinner
string edit
string edit_food_item
string error
string error_analyzing_image
string error_processing_image
string error_saving_food_entry
string fat
string fiber
string food_entry_saved
string food_name
string grams
string invalid_input
string kcal
string loading
string lunch
string milligrams
string mixed_meal
string no_analysis_result
string no_food_detected
string no_food_items_to_save
string nutrient_format
string ok
string photo_capture_failed
string protein
string retry
string review_food_entry
string save
string save_entry
string serving_size
string serving_unit
string snack
string sodium
string sugar
string take_photo
string tap_to_edit_instruction
string todays_meals
string todays_summary
string total_calories
string total_calories_format
style Theme.CalorieTracker
style Theme.CalorieTracker.Camera
xml backup_rules
xml data_extraction_rules
