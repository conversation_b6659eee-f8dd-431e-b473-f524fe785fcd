<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_editable_food" modulePackage="com.calorietracker.app" filePath="app\src\main\res\layout\item_editable_food.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_editable_food_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="172" endOffset="51"/></Target><Target id="@+id/text_food_name" view="TextView"><Expressions/><location startLine="32" startOffset="16" endLine="39" endOffset="57"/></Target><Target id="@+id/text_serving_size" view="TextView"><Expressions/><location startLine="41" startOffset="16" endLine="48" endOffset="40"/></Target><Target id="@+id/text_calories" view="TextView"><Expressions/><location startLine="58" startOffset="16" endLine="66" endOffset="43"/></Target><Target id="@+id/button_edit" view="ImageButton"><Expressions/><location startLine="68" startOffset="16" endLine="75" endOffset="53"/></Target><Target id="@+id/text_macros" view="TextView"><Expressions/><location startLine="82" startOffset="8" endLine="89" endOffset="55"/></Target></Targets></Layout>