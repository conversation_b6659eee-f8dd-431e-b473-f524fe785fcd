<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_camera" modulePackage="com.calorietracker.app" filePath="app\src\main\res\layout\activity_camera.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_camera_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="108" endOffset="51"/></Target><Target id="@+id/viewFinder" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/button_back" view="ImageButton"><Expressions/><location startLine="30" startOffset="8" endLine="37" endOffset="45"/></Target><Target id="@+id/image_capture_button" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="67" startOffset="8" endLine="75" endOffset="53"/></Target><Target id="@+id/progress_bar" view="LinearLayout"><Expressions/><location startLine="80" startOffset="4" endLine="106" endOffset="18"/></Target></Targets></Layout>