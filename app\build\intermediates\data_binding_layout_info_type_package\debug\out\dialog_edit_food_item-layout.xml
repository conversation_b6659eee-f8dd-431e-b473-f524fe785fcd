<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_edit_food_item" modulePackage="com.calorietracker.app" filePath="app\src\main\res\layout\dialog_edit_food_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/dialog_edit_food_item_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="212" endOffset="39"/></Target><Target id="@+id/edit_text_food_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="20" startOffset="12" endLine="24" endOffset="50"/></Target><Target id="@+id/edit_text_serving_size" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="42" startOffset="16" endLine="46" endOffset="55"/></Target><Target id="@+id/edit_text_serving_unit" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="16" endLine="61" endOffset="46"/></Target><Target id="@+id/edit_text_calories" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="74" startOffset="12" endLine="78" endOffset="51"/></Target><Target id="@+id/edit_text_protein" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="106" startOffset="16" endLine="110" endOffset="55"/></Target><Target id="@+id/edit_text_fat" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="121" startOffset="16" endLine="125" endOffset="55"/></Target><Target id="@+id/edit_text_carbohydrates" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="136" startOffset="16" endLine="140" endOffset="55"/></Target><Target id="@+id/edit_text_fiber" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="170" startOffset="16" endLine="174" endOffset="55"/></Target><Target id="@+id/edit_text_sugar" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="185" startOffset="16" endLine="189" endOffset="55"/></Target><Target id="@+id/edit_text_sodium" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="202" startOffset="12" endLine="206" endOffset="51"/></Target></Targets></Layout>