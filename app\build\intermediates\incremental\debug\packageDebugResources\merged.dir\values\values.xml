<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="accent_color">#FFFF9800</color>
    <color name="black">#FF000000</color>
    <color name="carbs_color">#FF2196F3</color>
    <color name="card_background">#FFFAFAFA</color>
    <color name="error_color">#FFF44336</color>
    <color name="fat_color">#FFFF9800</color>
    <color name="overlay_background">#80000000</color>
    <color name="primary_color">#FF4CAF50</color>
    <color name="primary_color_dark">#FF388E3C</color>
    <color name="protein_color">#FFE91E63</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="success_color">#FF4CAF50</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="warning_color">#FFFF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="add_food">Add Food</string>
    <string name="analysis_failed">Analysis failed: %s</string>
    <string name="analyzing_food">Analyzing food...</string>
    <string name="app_name">Calorie Tracker</string>
    <string name="back">Back</string>
    <string name="breakfast">Breakfast</string>
    <string name="calories">Calories</string>
    <string name="camera_instructions">Point camera at your food and tap to capture</string>
    <string name="camera_permission_required">Camera permission is required</string>
    <string name="cancel">Cancel</string>
    <string name="carbs">Carbs</string>
    <string name="delete">Delete</string>
    <string name="detected_food_items">Detected Food Items</string>
    <string name="dinner">Dinner</string>
    <string name="edit">Edit</string>
    <string name="edit_food_item">Edit Food Item</string>
    <string name="error">Error</string>
    <string name="error_analyzing_image">Error analyzing image: %s</string>
    <string name="error_processing_image">Error processing image: %s</string>
    <string name="error_saving_food_entry">Error saving food entry: %s</string>
    <string name="fat">Fat</string>
    <string name="fiber">Fiber</string>
    <string name="food_entry_saved">Food entry saved!</string>
    <string name="food_name">Food Name</string>
    <string name="grams">g</string>
    <string name="invalid_input">Please check your input values</string>
    <string name="kcal">kcal</string>
    <string name="loading">Loading...</string>
    <string name="lunch">Lunch</string>
    <string name="milligrams">mg</string>
    <string name="mixed_meal">Mixed meal (%d items)</string>
    <string name="no_analysis_result">No analysis result found</string>
    <string name="no_food_detected">No food items detected in the image. Please try again with a clearer photo.</string>
    <string name="no_food_items_to_save">No food items to save</string>
    <string name="nutrient_format">%.1f g</string>
    <string name="ok">OK</string>
    <string name="photo_capture_failed">Photo capture failed</string>
    <string name="protein">Protein</string>
    <string name="retry">Retry</string>
    <string name="review_food_entry">Review Food Entry</string>
    <string name="save">Save</string>
    <string name="save_entry">Save Entry</string>
    <string name="serving_size">Serving Size</string>
    <string name="serving_unit">Unit</string>
    <string name="snack">Snack</string>
    <string name="sodium">Sodium</string>
    <string name="sugar">Sugar</string>
    <string name="take_photo">Take Photo</string>
    <string name="tap_to_edit_instruction">Tap any item to edit its nutritional information</string>
    <string name="todays_meals">Today\'s Meals</string>
    <string name="todays_summary">Today\'s Summary</string>
    <string name="total_calories">Total Calories</string>
    <string name="total_calories_format">%d kcal</string>
    <style name="Theme.CalorieTracker" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_color_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/accent_color</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <item name="android:windowLightStatusBar" ns1:targetApi="m">false</item>
    </style>
    <style name="Theme.CalorieTracker.Camera" parent="Theme.CalorieTracker">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>
</resources>