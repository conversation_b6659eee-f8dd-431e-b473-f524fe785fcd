[{"merged": "com.calorietracker.app-merged_res-63:/layout_dialog_edit_food_item.xml.flat", "source": "com.calorietracker.app-main-65:/layout/dialog_edit_food_item.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/layout_item_editable_food.xml.flat", "source": "com.calorietracker.app-main-65:/layout/item_editable_food.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/layout_activity_camera.xml.flat", "source": "com.calorietracker.app-main-65:/layout/activity_camera.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/drawable_ic_launcher_foreground.xml.flat", "source": "com.calorietracker.app-main-65:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.calorietracker.app-main-65:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/layout_activity_food_entry.xml.flat", "source": "com.calorietracker.app-main-65:/layout/activity_food_entry.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/xml_backup_rules.xml.flat", "source": "com.calorietracker.app-main-65:/xml/backup_rules.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/drawable_rounded_background.xml.flat", "source": "com.calorietracker.app-main-65:/drawable/rounded_background.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/drawable_ic_launcher_background.xml.flat", "source": "com.calorietracker.app-main-65:/drawable/ic_launcher_background.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/drawable_ic_camera.xml.flat", "source": "com.calorietracker.app-main-65:/drawable/ic_camera.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.calorietracker.app-main-65:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/xml_data_extraction_rules.xml.flat", "source": "com.calorietracker.app-main-65:/xml/data_extraction_rules.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/drawable_ic_arrow_back.xml.flat", "source": "com.calorietracker.app-main-65:/drawable/ic_arrow_back.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/layout_activity_main.xml.flat", "source": "com.calorietracker.app-main-65:/layout/activity_main.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/layout_item_food_entry.xml.flat", "source": "com.calorietracker.app-main-65:/layout/item_food_entry.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/drawable_circle_background.xml.flat", "source": "com.calorietracker.app-main-65:/drawable/circle_background.xml"}, {"merged": "com.calorietracker.app-merged_res-63:/drawable_ic_edit.xml.flat", "source": "com.calorietracker.app-main-65:/drawable/ic_edit.xml"}]