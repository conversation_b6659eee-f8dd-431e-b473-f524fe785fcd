#Fri Jun 20 20:05:33 TRT 2025
com.calorietracker.app-main-65\:/drawable/circle_background.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.calorietracker.app-main-65\:/drawable/ic_arrow_back.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_arrow_back.xml.flat
com.calorietracker.app-main-65\:/drawable/ic_camera.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_camera.xml.flat
com.calorietracker.app-main-65\:/drawable/ic_edit.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_edit.xml.flat
com.calorietracker.app-main-65\:/drawable/ic_launcher_background.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.calorietracker.app-main-65\:/drawable/ic_launcher_foreground.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.calorietracker.app-main-65\:/drawable/rounded_background.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background.xml.flat
com.calorietracker.app-main-65\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.calorietracker.app-main-65\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.calorietracker.app-main-65\:/xml/backup_rules.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\xml_backup_rules.xml.flat
com.calorietracker.app-main-65\:/xml/data_extraction_rules.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\xml_data_extraction_rules.xml.flat
com.calorietracker.app-mergeDebugResources-62\:/layout/activity_camera.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_camera.xml.flat
com.calorietracker.app-mergeDebugResources-62\:/layout/activity_food_entry.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_food_entry.xml.flat
com.calorietracker.app-mergeDebugResources-62\:/layout/activity_main.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.calorietracker.app-mergeDebugResources-62\:/layout/dialog_edit_food_item.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_edit_food_item.xml.flat
com.calorietracker.app-mergeDebugResources-62\:/layout/item_editable_food.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\layout_item_editable_food.xml.flat
com.calorietracker.app-mergeDebugResources-62\:/layout/item_food_entry.xml=D\:\\CalorieTrackerApp\\CalorieTrackerApp\\app\\build\\intermediates\\merged_res\\debug\\layout_item_food_entry.xml.flat
