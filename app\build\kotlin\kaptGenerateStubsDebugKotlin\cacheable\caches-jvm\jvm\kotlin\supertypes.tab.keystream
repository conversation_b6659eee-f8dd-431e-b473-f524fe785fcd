0com.calorietracker.app.CalorieTrackerApplication9com.calorietracker.app.data.api.models.FoodAnalysisResult/com.calorietracker.app.data.api.models.FoodItem0com.calorietracker.app.data.database.AppDatabase&com.calorietracker.app.ui.MainActivity/com.calorietracker.app.ui.camera.CameraActivity0com.calorietracker.app.ui.camera.CameraViewModel<com.calorietracker.app.ui.camera.FoodAnalysisResultWithImage6com.calorietracker.app.ui.foodentry.EditFoodItemDialog5com.calorietracker.app.ui.foodentry.FoodEntryActivity6com.calorietracker.app.ui.foodentry.FoodEntryViewModel4com.calorietracker.app.ui.foodentry.EditableFoodItem3com.calorietracker.app.ui.foodentry.FoodItemAdapterFcom.calorietracker.app.ui.foodentry.FoodItemAdapter.FoodItemViewHolder8com.calorietracker.app.ui.foodentry.FoodItemDiffCallback/com.calorietracker.app.ui.home.FoodEntryAdapterCcom.calorietracker.app.ui.home.FoodEntryAdapter.FoodEntryViewHolder4com.calorietracker.app.ui.home.FoodEntryDiffCallback,com.calorietracker.app.ui.home.HomeViewModel:com.calorietracker.app.databinding.ItemEditableFoodBinding7com.calorietracker.app.databinding.ItemFoodEntryBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         