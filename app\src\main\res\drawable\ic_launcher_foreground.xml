<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    <group android:scaleX="2"
        android:scaleY="2"
        android:pivotX="54"
        android:pivotY="54">
        <!-- Food/Apple icon representing nutrition tracking -->
        <path android:fillColor="@android:color/white"
            android:pathData="M35,21c-1.1,0 -2,0.9 -2,2v18c0,1.1 0.9,2 2,2h14c1.1,0 2,-0.9 2,-2V23c0,-1.1 -0.9,-2 -2,-2H35zM42,19c0.83,0 1.5,-0.67 1.5,-1.5 0,-0.83 -0.67,-1.5 -1.5,-1.5s-1.5,0.67 -1.5,1.5c0,0.83 0.67,1.5 1.5,1.5z"/>
        <!-- Camera icon overlay -->
        <path android:fillColor="@android:color/white"
            android:pathData="M27,12m-3.2,0a3.2,3.2 0,1 1,6.4 0a3.2,3.2 0,1 1,-6.4 0"
            android:fillAlpha="0.7"/>
        <path android:fillColor="@android:color/white"
            android:pathData="M21,4L19.17,6L16,6c-1.1,0 -2,0.9 -2,2v12c0,1.1 0.9,2 2,2h22c1.1,0 2,-0.9 2,-2L40,8c0,-1.1 -0.9,-2 -2,-2h-3.17L33,4L21,4zM27,19c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5z"
            android:fillAlpha="0.7"/>
    </group>
</vector>
