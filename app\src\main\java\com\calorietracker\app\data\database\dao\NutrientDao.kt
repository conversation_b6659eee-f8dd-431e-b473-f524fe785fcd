package com.calorietracker.app.data.database.dao

import androidx.room.*
import com.calorietracker.app.data.database.entities.Nutrient
import kotlinx.coroutines.flow.Flow

@Dao
interface NutrientDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(nutrients: List<Nutrient>)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(nutrient: Nutrient): Long

    @Query("SELECT * FROM nutrients WHERE foodEntryId = :foodEntryId")
    suspend fun getNutrientsForFoodEntry(foodEntryId: Long): List<Nutrient>
    
    @Query("SELECT * FROM nutrients WHERE foodEntryId = :foodEntryId")
    fun getNutrientsForFoodEntryFlow(foodEntryId: Long): Flow<List<Nutrient>>
    
    @Query("SELECT SUM(amount) FROM nutrients WHERE name = :nutrientName AND foodEntryId IN (SELECT id FROM food_entries WHERE DATE(timestamp/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch'))")
    suspend fun getTotalNutrientForDate(nutrientName: String, date: Long): Double?
    
    @Update
    suspend fun update(nutrient: Nutrient)
    
    @Delete
    suspend fun delete(nutrient: Nutrient)
    
    @Query("DELETE FROM nutrients WHERE foodEntryId = :foodEntryId")
    suspend fun deleteByFoodEntryId(foodEntryId: Long)
}
