1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.calorietracker.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:6:5-65
12-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:7:5-67
13-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:9:5-80
15-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:9:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:10:5-81
16-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:10:22-78
17
18    <!-- Camera features -->
19    <uses-feature
19-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:13:5-15:35
20        android:name="android.hardware.camera"
20-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:14:9-47
21        android:required="true" />
21-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:15:9-32
22    <uses-feature
22-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:16:5-18:36
23        android:name="android.hardware.camera.autofocus"
23-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:17:9-57
24        android:required="false" />
24-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:18:9-33
25
26    <queries>
26-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
27        <intent>
27-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
28            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
28-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
28-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
29        </intent>
30    </queries>
31
32    <permission
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
33        android:name="com.calorietracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.calorietracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
37
38    <application
38-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:20:5-51:19
39        android:name="com.calorietracker.app.CalorieTrackerApplication"
39-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:21:9-50
40        android:allowBackup="true"
40-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:22:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
42        android:dataExtractionRules="@xml/data_extraction_rules"
42-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:23:9-65
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:fullBackupContent="@xml/backup_rules"
45-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:24:9-54
46        android:icon="@mipmap/ic_launcher"
46-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:25:9-43
47        android:label="@string/app_name"
47-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:26:9-41
48        android:roundIcon="@mipmap/ic_launcher_round"
48-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:27:9-54
49        android:supportsRtl="true"
49-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:28:9-35
50        android:testOnly="true"
51        android:theme="@style/Theme.CalorieTracker"
51-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:29:9-52
52        android:usesCleartextTraffic="true" >
52-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:30:9-44
53        <activity
53-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:33:9-41:20
54            android:name="com.calorietracker.app.ui.MainActivity"
54-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:34:13-44
55            android:exported="true"
55-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:35:13-36
56            android:theme="@style/Theme.CalorieTracker" >
56-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:36:13-56
57            <intent-filter>
57-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:37:13-40:29
58                <action android:name="android.intent.action.MAIN" />
58-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:38:17-69
58-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:38:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:39:17-77
60-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:39:27-74
61            </intent-filter>
62        </activity>
63        <activity
63-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:43:9-46:52
64            android:name="com.calorietracker.app.ui.camera.CameraActivity"
64-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:44:13-53
65            android:exported="false"
65-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:45:13-37
66            android:screenOrientation="portrait" />
66-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:46:13-49
67        <activity
67-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:48:9-50:40
68            android:name="com.calorietracker.app.ui.foodentry.FoodEntryActivity"
68-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:49:13-59
69            android:exported="false" />
69-->D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:50:13-37
70
71        <uses-library
71-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
72            android:name="androidx.camera.extensions.impl"
72-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
73            android:required="false" />
73-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
74
75        <service
75-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
76            android:name="androidx.camera.core.impl.MetadataHolderService"
76-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
77            android:enabled="false"
77-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
78            android:exported="false" >
78-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
79            <meta-data
79-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
80                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
80-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
81                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
81-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
82        </service>
83
84        <activity
84-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:12:9-16:66
85            android:name="pub.devrel.easypermissions.AppSettingsDialogHolderActivity"
85-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:13:13-86
86            android:exported="false"
86-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:14:13-37
87            android:label=""
87-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:15:13-29
88            android:theme="@style/EasyPermissions.Transparent" />
88-->[pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:16:13-63
89
90        <provider
90-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
91            android:name="androidx.startup.InitializationProvider"
91-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
92            android:authorities="com.calorietracker.app.androidx-startup"
92-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
93            android:exported="false" >
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
94            <meta-data
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.emoji2.text.EmojiCompatInitializer"
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
96                android:value="androidx.startup" />
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
98-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
99                android:value="androidx.startup" />
99-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
102                android:value="androidx.startup" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
103        </provider>
104
105        <uses-library
105-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
106            android:name="androidx.window.extensions"
106-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
107            android:required="false" />
107-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
108        <uses-library
108-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
109            android:name="androidx.window.sidecar"
109-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
110            android:required="false" />
110-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
111
112        <service
112-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
113            android:name="androidx.room.MultiInstanceInvalidationService"
113-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
114            android:directBootAware="true"
114-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
115            android:exported="false" />
115-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
116
117        <receiver
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
118            android:name="androidx.profileinstaller.ProfileInstallReceiver"
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
119            android:directBootAware="false"
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
120            android:enabled="true"
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
121            android:exported="true"
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
122            android:permission="android.permission.DUMP" >
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
123            <intent-filter>
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
124                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
125            </intent-filter>
126            <intent-filter>
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
127                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
128            </intent-filter>
129            <intent-filter>
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
130                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
131            </intent-filter>
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
133                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
133-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
134            </intent-filter>
135        </receiver>
136    </application>
137
138</manifest>
