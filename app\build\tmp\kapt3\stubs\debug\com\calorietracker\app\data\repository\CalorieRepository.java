package com.calorietracker.app.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ$\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0013H\u0002J\u0016\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0012\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0\u001dJ\u001a\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\u0006\u0010!\u001a\u00020\u001aJ\"\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\u0006\u0010#\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020\u001aJ\u0018\u0010%\u001a\u0004\u0018\u00010\u001f2\u0006\u0010&\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010\'\u001a\b\u0012\u0004\u0012\u00020(0\u001e2\u0006\u0010\u0019\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0016\u0010)\u001a\u00020*2\u0006\u0010!\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u001e\u0010+\u001a\u00020*2\u0006\u0010,\u001a\u00020\u00132\u0006\u0010!\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010-J\u0010\u0010.\u001a\u00020\r2\u0006\u0010/\u001a\u000200H\u0002J\"\u00101\u001a\u00020\u001a2\u0006\u00102\u001a\u00020\r2\n\b\u0002\u00103\u001a\u0004\u0018\u00010\u0013H\u0086@\u00a2\u0006\u0002\u00104J\u0016\u00105\u001a\u00020\u00182\u0006\u00106\u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u00107R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00068"}, d2 = {"Lcom/calorietracker/app/data/repository/CalorieRepository;", "", "geminiApiService", "Lcom/calorietracker/app/data/api/GeminiApiService;", "foodEntryDao", "Lcom/calorietracker/app/data/database/dao/FoodEntryDao;", "nutrientDao", "Lcom/calorietracker/app/data/database/dao/NutrientDao;", "gson", "Lcom/google/gson/Gson;", "(Lcom/calorietracker/app/data/api/GeminiApiService;Lcom/calorietracker/app/data/database/dao/FoodEntryDao;Lcom/calorietracker/app/data/database/dao/NutrientDao;Lcom/google/gson/Gson;)V", "analyzeFoodImage", "Lkotlin/Result;", "Lcom/calorietracker/app/data/api/models/FoodAnalysisResult;", "bitmap", "Landroid/graphics/Bitmap;", "analyzeFoodImage-gIAlu-s", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "bitmapToBase64", "", "createGeminiRequest", "Lcom/calorietracker/app/data/api/models/GeminiRequest;", "base64Image", "deleteFoodEntry", "", "foodEntryId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllFoodEntries", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/calorietracker/app/data/database/entities/FoodEntry;", "getFoodEntriesForDate", "date", "getFoodEntriesInRange", "startTime", "endTime", "getFoodEntry", "id", "getNutrientsForFoodEntry", "Lcom/calorietracker/app/data/database/entities/Nutrient;", "getTotalCaloriesForDate", "", "getTotalNutrientForDate", "nutrientName", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "parseGeminiResponse", "response", "Lcom/calorietracker/app/data/api/models/GeminiResponse;", "saveFoodEntry", "foodAnalysisResult", "imageUri", "(Lcom/calorietracker/app/data/api/models/FoodAnalysisResult;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateFoodEntry", "foodEntry", "(Lcom/calorietracker/app/data/database/entities/FoodEntry;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class CalorieRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.calorietracker.app.data.api.GeminiApiService geminiApiService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.calorietracker.app.data.database.dao.FoodEntryDao foodEntryDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.calorietracker.app.data.database.dao.NutrientDao nutrientDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    
    @javax.inject.Inject()
    public CalorieRepository(@org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.api.GeminiApiService geminiApiService, @org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.database.dao.FoodEntryDao foodEntryDao, @org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.database.dao.NutrientDao nutrientDao, @org.jetbrains.annotations.NotNull()
    com.google.gson.Gson gson) {
        super();
    }
    
    private final java.lang.String bitmapToBase64(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    private final com.calorietracker.app.data.api.models.GeminiRequest createGeminiRequest(java.lang.String base64Image) {
        return null;
    }
    
    private final com.calorietracker.app.data.api.models.FoodAnalysisResult parseGeminiResponse(com.calorietracker.app.data.api.models.GeminiResponse response) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveFoodEntry(@org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.api.models.FoodAnalysisResult foodAnalysisResult, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUri, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateFoodEntry(@org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.database.entities.FoodEntry foodEntry, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteFoodEntry(long foodEntryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.calorietracker.app.data.database.entities.FoodEntry>> getAllFoodEntries() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.calorietracker.app.data.database.entities.FoodEntry>> getFoodEntriesForDate(long date) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getFoodEntry(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.calorietracker.app.data.database.entities.FoodEntry> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getNutrientsForFoodEntry(long foodEntryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.calorietracker.app.data.database.entities.Nutrient>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalCaloriesForDate(long date, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalNutrientForDate(@org.jetbrains.annotations.NotNull()
    java.lang.String nutrientName, long date, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.calorietracker.app.data.database.entities.FoodEntry>> getFoodEntriesInRange(long startTime, long endTime) {
        return null;
    }
}