package com.calorietracker.app.ui.foodentry;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = FoodEntryActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface FoodEntryActivity_GeneratedInjector {
  void injectFoodEntryActivity(FoodEntryActivity foodEntryActivity);
}
