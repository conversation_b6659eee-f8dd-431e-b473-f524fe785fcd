// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.calorietracker.app.di;

import com.calorietracker.app.data.api.GeminiApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideGeminiApiServiceFactory implements Factory<GeminiApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideGeminiApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public GeminiApiService get() {
    return provideGeminiApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideGeminiApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideGeminiApiServiceFactory(retrofitProvider);
  }

  public static GeminiApiService provideGeminiApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideGeminiApiService(retrofit));
  }
}
