package com.calorietracker.app.ui.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.calorietracker.app.data.database.entities.FoodEntry
import com.calorietracker.app.databinding.ItemFoodEntryBinding
import java.text.SimpleDateFormat
import java.util.*

class FoodEntryAdapter(
    private val onItemClick: (FoodEntry) -> Unit
) : ListAdapter<FoodEntry, FoodEntryAdapter.FoodEntryViewHolder>(FoodEntryDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FoodEntryViewHolder {
        val binding = ItemFoodEntryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return FoodEntryViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FoodEntryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class FoodEntryViewHolder(
        private val binding: ItemFoodEntryBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(foodEntry: FoodEntry) {
            binding.apply {
                textFoodName.text = foodEntry.name
                textCalories.text = "%.0f kcal".format(foodEntry.totalCalories)
                textServingSize.text = "%.0f g".format(foodEntry.servingSize)
                
                val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                textTime.text = timeFormat.format(Date(foodEntry.timestamp))
                
                root.setOnClickListener {
                    onItemClick(foodEntry)
                }
            }
        }
    }
}

class FoodEntryDiffCallback : DiffUtil.ItemCallback<FoodEntry>() {
    override fun areItemsTheSame(oldItem: FoodEntry, newItem: FoodEntry): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: FoodEntry, newItem: FoodEntry): Boolean {
        return oldItem == newItem
    }
}
