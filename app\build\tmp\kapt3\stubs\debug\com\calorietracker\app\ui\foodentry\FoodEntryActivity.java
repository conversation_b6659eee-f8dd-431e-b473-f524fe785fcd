package com.calorietracker.app.ui.foodentry;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\r\u001a\u00020\u000eH\u0002J\u0012\u0010\u000f\u001a\u00020\u000e2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0014J\b\u0010\u0012\u001a\u00020\u000eH\u0002J\b\u0010\u0013\u001a\u00020\u000eH\u0002J\u0010\u0010\u0014\u001a\u00020\u000e2\u0006\u0010\u0015\u001a\u00020\u0016H\u0002J\u0016\u0010\u0017\u001a\u00020\u000e2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00160\u0019H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\n\u00a8\u0006\u001a"}, d2 = {"Lcom/calorietracker/app/ui/foodentry/FoodEntryActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "binding", "Lcom/calorietracker/app/databinding/ActivityFoodEntryBinding;", "foodItemAdapter", "Lcom/calorietracker/app/ui/foodentry/FoodItemAdapter;", "viewModel", "Lcom/calorietracker/app/ui/foodentry/FoodEntryViewModel;", "getViewModel", "()Lcom/calorietracker/app/ui/foodentry/FoodEntryViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "loadAnalysisResult", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "setupObservers", "setupUI", "showEditFoodItemDialog", "foodItem", "Lcom/calorietracker/app/ui/foodentry/EditableFoodItem;", "updateTotalCalories", "foodItems", "", "app_debug"})
public final class FoodEntryActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.calorietracker.app.databinding.ActivityFoodEntryBinding binding;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    private com.calorietracker.app.ui.foodentry.FoodItemAdapter foodItemAdapter;
    
    public FoodEntryActivity() {
        super();
    }
    
    private final com.calorietracker.app.ui.foodentry.FoodEntryViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupObservers() {
    }
    
    private final void loadAnalysisResult() {
    }
    
    private final void updateTotalCalories(java.util.List<com.calorietracker.app.ui.foodentry.EditableFoodItem> foodItems) {
    }
    
    private final void showEditFoodItemDialog(com.calorietracker.app.ui.foodentry.EditableFoodItem foodItem) {
    }
}