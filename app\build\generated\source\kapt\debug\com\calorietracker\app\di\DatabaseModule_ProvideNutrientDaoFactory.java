// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.calorietracker.app.di;

import com.calorietracker.app.data.database.AppDatabase;
import com.calorietracker.app.data.database.dao.NutrientDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideNutrientDaoFactory implements Factory<NutrientDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideNutrientDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public NutrientDao get() {
    return provideNutrientDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideNutrientDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideNutrientDaoFactory(databaseProvider);
  }

  public static NutrientDao provideNutrientDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideNutrientDao(database));
  }
}
