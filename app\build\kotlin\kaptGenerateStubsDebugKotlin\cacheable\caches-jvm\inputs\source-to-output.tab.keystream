Kapp/src/main/java/com/calorietracker/app/ui/foodentry/EditFoodItemDialog.ktEapp/src/main/java/com/calorietracker/app/ui/camera/CameraViewModel.ktJapp/src/main/java/com/calorietracker/app/data/api/models/GeminiResponse.ktIapp/src/main/java/com/calorietracker/app/data/database/dao/NutrientDao.ktLapp/src/main/java/com/calorietracker/app/data/database/entities/FoodEntry.kt=app/src/main/java/com/calorietracker/app/di/DatabaseModule.ktEapp/src/main/java/com/calorietracker/app/data/api/GeminiApiService.ktEapp/src/main/java/com/calorietracker/app/data/database/AppDatabase.kt<app/src/main/java/com/calorietracker/app/di/NetworkModule.kt;app/src/main/java/com/calorietracker/app/ui/MainActivity.ktDapp/src/main/java/com/calorietracker/app/ui/camera/CameraActivity.ktHapp/src/main/java/com/calorietracker/app/ui/foodentry/FoodItemAdapter.ktJapp/src/main/java/com/calorietracker/app/ui/foodentry/FoodEntryActivity.ktAapp/src/main/java/com/calorietracker/app/ui/home/<USER>/src/main/java/com/calorietracker/app/data/repository/CalorieRepository.ktIapp/src/main/java/com/calorietracker/app/data/api/models/GeminiRequest.ktDapp/src/main/java/com/calorietracker/app/ui/home/<USER>/src/main/java/com/calorietracker/app/data/database/dao/FoodEntryDao.ktEapp/src/main/java/com/calorietracker/app/CalorieTrackerApplication.ktKapp/src/main/java/com/calorietracker/app/ui/foodentry/FoodEntryViewModel.ktKapp/src/main/java/com/calorietracker/app/data/database/entities/Nutrient.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        