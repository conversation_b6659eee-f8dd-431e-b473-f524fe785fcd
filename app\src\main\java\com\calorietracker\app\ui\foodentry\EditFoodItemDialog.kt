package com.calorietracker.app.ui.foodentry

import android.app.Dialog
import android.os.Bundle
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.calorietracker.app.databinding.DialogEditFoodItemBinding
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class EditFoodItemDialog : DialogFragment() {

    private var _binding: DialogEditFoodItemBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var foodItem: EditableFoodItem
    private lateinit var onSave: (EditableFoodItem) -> Unit

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogEditFoodItemBinding.inflate(layoutInflater)
        
        setupViews()
        
        return MaterialAlertDialogBuilder(requireContext())
            .setTitle("Edit Food Item")
            .setView(binding.root)
            .setPositiveButton("Save") { _, _ ->
                saveChanges()
            }
            .setNegativeButton("Cancel", null)
            .create()
    }

    private fun setupViews() {
        binding.apply {
            editTextFoodName.setText(foodItem.name)
            editTextServingSize.setText(foodItem.servingSize.toString())
            editTextServingUnit.setText(foodItem.servingUnit)
            editTextCalories.setText(foodItem.calories.toString())
            editTextProtein.setText(foodItem.protein.toString())
            editTextFat.setText(foodItem.fat.toString())
            editTextCarbohydrates.setText(foodItem.carbohydrates.toString())
            
            // Optional nutrients
            foodItem.fiber?.let { editTextFiber.setText(it.toString()) }
            foodItem.sugar?.let { editTextSugar.setText(it.toString()) }
            foodItem.sodium?.let { editTextSodium.setText(it.toString()) }
        }
    }

    private fun saveChanges() {
        try {
            val updatedFoodItem = foodItem.copy(
                name = binding.editTextFoodName.text.toString().trim(),
                servingSize = binding.editTextServingSize.text.toString().toDoubleOrNull() ?: 0.0,
                servingUnit = binding.editTextServingUnit.text.toString().trim(),
                calories = binding.editTextCalories.text.toString().toDoubleOrNull() ?: 0.0,
                protein = binding.editTextProtein.text.toString().toDoubleOrNull() ?: 0.0,
                fat = binding.editTextFat.text.toString().toDoubleOrNull() ?: 0.0,
                carbohydrates = binding.editTextCarbohydrates.text.toString().toDoubleOrNull() ?: 0.0,
                fiber = binding.editTextFiber.text.toString().toDoubleOrNull(),
                sugar = binding.editTextSugar.text.toString().toDoubleOrNull(),
                sodium = binding.editTextSodium.text.toString().toDoubleOrNull()
            )
            
            // Validate input
            if (updatedFoodItem.name.isEmpty() || 
                updatedFoodItem.servingSize <= 0 || 
                updatedFoodItem.calories < 0) {
                Toast.makeText(context, "Please check your input values", Toast.LENGTH_SHORT).show()
                return
            }
            
            onSave(updatedFoodItem)
        } catch (e: Exception) {
            Toast.makeText(context, "Invalid input values", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        fun newInstance(
            foodItem: EditableFoodItem,
            onSave: (EditableFoodItem) -> Unit
        ): EditFoodItemDialog {
            return EditFoodItemDialog().apply {
                this.foodItem = foodItem
                this.onSave = onSave
            }
        }
    }
}
