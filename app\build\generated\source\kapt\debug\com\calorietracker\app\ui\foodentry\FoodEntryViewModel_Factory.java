// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.calorietracker.app.ui.foodentry;

import com.calorietracker.app.data.repository.CalorieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FoodEntryViewModel_Factory implements Factory<FoodEntryViewModel> {
  private final Provider<CalorieRepository> repositoryProvider;

  public FoodEntryViewModel_Factory(Provider<CalorieRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public FoodEntryViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static FoodEntryViewModel_Factory create(Provider<CalorieRepository> repositoryProvider) {
    return new FoodEntryViewModel_Factory(repositoryProvider);
  }

  public static FoodEntryViewModel newInstance(CalorieRepository repository) {
    return new FoodEntryViewModel(repository);
  }
}
