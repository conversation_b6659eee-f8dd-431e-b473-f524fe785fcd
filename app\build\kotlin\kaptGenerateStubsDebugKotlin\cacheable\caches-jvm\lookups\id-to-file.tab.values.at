/ Header Record For PersistentHashMapValueStorageF Eapp/src/main/java/com/calorietracker/app/CalorieTrackerApplication.ktF Eapp/src/main/java/com/calorietracker/app/data/api/GeminiApiService.ktJ Iapp/src/main/java/com/calorietracker/app/data/api/models/GeminiRequest.ktK Japp/src/main/java/com/calorietracker/app/data/api/models/GeminiResponse.ktF Eapp/src/main/java/com/calorietracker/app/data/database/AppDatabase.ktK Japp/src/main/java/com/calorietracker/app/data/database/dao/FoodEntryDao.ktJ Iapp/src/main/java/com/calorietracker/app/data/database/dao/NutrientDao.ktM Lapp/src/main/java/com/calorietracker/app/data/database/entities/FoodEntry.ktL Kapp/src/main/java/com/calorietracker/app/data/database/entities/Nutrient.ktN Mapp/src/main/java/com/calorietracker/app/data/repository/CalorieRepository.kt> =app/src/main/java/com/calorietracker/app/di/DatabaseModule.kt= <app/src/main/java/com/calorietracker/app/di/NetworkModule.kt< ;app/src/main/java/com/calorietracker/app/ui/MainActivity.ktE Dapp/src/main/java/com/calorietracker/app/ui/camera/CameraActivity.ktF Eapp/src/main/java/com/calorietracker/app/ui/camera/CameraViewModel.ktL Kapp/src/main/java/com/calorietracker/app/ui/foodentry/EditFoodItemDialog.ktK Japp/src/main/java/com/calorietracker/app/ui/foodentry/FoodEntryActivity.ktL Kapp/src/main/java/com/calorietracker/app/ui/foodentry/FoodEntryViewModel.ktI Happ/src/main/java/com/calorietracker/app/ui/foodentry/FoodItemAdapter.ktE Dapp/src/main/java/com/calorietracker/app/ui/home/<USER>/src/main/java/com/calorietracker/app/ui/home/<USER>