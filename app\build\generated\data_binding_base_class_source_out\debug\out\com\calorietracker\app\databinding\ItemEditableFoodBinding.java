// Generated by view binder compiler. Do not edit!
package com.calorietracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.calorietracker.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemEditableFoodBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton buttonEdit;

  @NonNull
  public final TextView textCalories;

  @NonNull
  public final TextView textFoodName;

  @NonNull
  public final TextView textMacros;

  @NonNull
  public final TextView textServingSize;

  private ItemEditableFoodBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton buttonEdit, @NonNull TextView textCalories,
      @NonNull TextView textFoodName, @NonNull TextView textMacros,
      @NonNull TextView textServingSize) {
    this.rootView = rootView;
    this.buttonEdit = buttonEdit;
    this.textCalories = textCalories;
    this.textFoodName = textFoodName;
    this.textMacros = textMacros;
    this.textServingSize = textServingSize;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemEditableFoodBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemEditableFoodBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_editable_food, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemEditableFoodBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_edit;
      ImageButton buttonEdit = ViewBindings.findChildViewById(rootView, id);
      if (buttonEdit == null) {
        break missingId;
      }

      id = R.id.text_calories;
      TextView textCalories = ViewBindings.findChildViewById(rootView, id);
      if (textCalories == null) {
        break missingId;
      }

      id = R.id.text_food_name;
      TextView textFoodName = ViewBindings.findChildViewById(rootView, id);
      if (textFoodName == null) {
        break missingId;
      }

      id = R.id.text_macros;
      TextView textMacros = ViewBindings.findChildViewById(rootView, id);
      if (textMacros == null) {
        break missingId;
      }

      id = R.id.text_serving_size;
      TextView textServingSize = ViewBindings.findChildViewById(rootView, id);
      if (textServingSize == null) {
        break missingId;
      }

      return new ItemEditableFoodBinding((MaterialCardView) rootView, buttonEdit, textCalories,
          textFoodName, textMacros, textServingSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
