package com.calorietracker.app.di

import android.content.Context
import androidx.room.Room
import com.calorietracker.app.data.database.AppDatabase
import com.calorietracker.app.data.database.dao.FoodEntryDao
import com.calorietracker.app.data.database.dao.NutrientDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            AppDatabase::class.java,
            "calorie_tracker_database"
        ).build()
    }

    @Provides
    fun provideFoodEntryDao(database: AppDatabase): FoodEntryDao {
        return database.foodEntryDao()
    }

    @Provides
    fun provideNutrientDao(database: AppDatabase): NutrientDao {
        return database.nutrientDao()
    }
}
