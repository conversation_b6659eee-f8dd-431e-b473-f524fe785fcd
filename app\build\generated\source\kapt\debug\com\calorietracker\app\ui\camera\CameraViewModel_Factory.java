// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.calorietracker.app.ui.camera;

import com.calorietracker.app.data.repository.CalorieRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CameraViewModel_Factory implements Factory<CameraViewModel> {
  private final Provider<CalorieRepository> repositoryProvider;

  public CameraViewModel_Factory(Provider<CalorieRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public CameraViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static CameraViewModel_Factory create(Provider<CalorieRepository> repositoryProvider) {
    return new CameraViewModel_Factory(repositoryProvider);
  }

  public static CameraViewModel newInstance(CalorieRepository repository) {
    return new CameraViewModel(repository);
  }
}
