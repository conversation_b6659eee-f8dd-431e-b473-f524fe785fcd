package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.calorietracker.app.CalorieTrackerApplication",
    rootPackage = "com.calorietracker.app",
    originatingRoot = "com.calorietracker.app.CalorieTrackerApplication",
    originatingRootPackage = "com.calorietracker.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "CalorieTrackerApplication",
    originatingRootSimpleNames = "CalorieTrackerApplication"
)
public class _com_calorietracker_app_CalorieTrackerApplication {
}
