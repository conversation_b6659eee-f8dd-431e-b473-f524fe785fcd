package com.calorietracker.app.ui.foodentry

import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.calorietracker.app.databinding.ActivityFoodEntryBinding
import com.calorietracker.app.ui.camera.FoodAnalysisResultWithImage
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class FoodEntryActivity : AppCompatActivity() {

    private lateinit var binding: ActivityFoodEntryBinding
    private val viewModel: FoodEntryViewModel by viewModels()
    private lateinit var foodItemAdapter: FoodItemAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFoodEntryBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupObservers()
        loadAnalysisResult()
    }

    private fun setupUI() {
        // Setup RecyclerView for food items
        foodItemAdapter = FoodItemAdapter { foodItem ->
            // Handle food item edit
            showEditFoodItemDialog(foodItem)
        }
        
        binding.recyclerViewFoodItems.apply {
            layoutManager = LinearLayoutManager(this@FoodEntryActivity)
            adapter = foodItemAdapter
        }

        // Setup buttons
        binding.buttonSave.setOnClickListener {
            viewModel.saveFoodEntry()
        }
        
        binding.buttonCancel.setOnClickListener {
            finish()
        }
        
        binding.buttonBack.setOnClickListener {
            finish()
        }
    }

    private fun setupObservers() {
        lifecycleScope.launch {
            viewModel.foodItems.collect { foodItems ->
                foodItemAdapter.submitList(foodItems)
                updateTotalCalories(foodItems)
            }
        }

        lifecycleScope.launch {
            viewModel.imagePath.collect { imagePath ->
                imagePath?.let {
                    Glide.with(this@FoodEntryActivity)
                        .load(it)
                        .into(binding.imageViewFood)
                }
            }
        }

        lifecycleScope.launch {
            viewModel.isLoading.collect { isLoading ->
                binding.progressBar.visibility = if (isLoading) {
                    android.view.View.VISIBLE
                } else {
                    android.view.View.GONE
                }
                binding.buttonSave.isEnabled = !isLoading
            }
        }

        lifecycleScope.launch {
            viewModel.saveComplete.collect { isComplete ->
                if (isComplete) {
                    Toast.makeText(this@FoodEntryActivity, "Food entry saved!", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        }

        lifecycleScope.launch {
            viewModel.errorMessage.collect { error ->
                error?.let {
                    Toast.makeText(this@FoodEntryActivity, it, Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun loadAnalysisResult() {
        val analysisResult = intent.getParcelableExtra<FoodAnalysisResultWithImage>("analysis_result")
        if (analysisResult != null) {
            viewModel.setAnalysisResult(analysisResult)
        } else {
            Toast.makeText(this, "No analysis result found", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun updateTotalCalories(foodItems: List<EditableFoodItem>) {
        val totalCalories = foodItems.sumOf { it.calories }
        binding.textTotalCalories.text = "Total: %.0f kcal".format(totalCalories)
    }

    private fun showEditFoodItemDialog(foodItem: EditableFoodItem) {
        val dialog = EditFoodItemDialog.newInstance(foodItem) { updatedFoodItem ->
            viewModel.updateFoodItem(updatedFoodItem)
        }
        dialog.show(supportFragmentManager, "EditFoodItemDialog")
    }
}
