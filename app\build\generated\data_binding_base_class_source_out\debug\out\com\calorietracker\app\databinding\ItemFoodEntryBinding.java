// Generated by view binder compiler. Do not edit!
package com.calorietracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.calorietracker.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFoodEntryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView textCalories;

  @NonNull
  public final TextView textFoodName;

  @NonNull
  public final TextView textServingSize;

  @NonNull
  public final TextView textTime;

  private ItemFoodEntryBinding(@NonNull MaterialCardView rootView, @NonNull TextView textCalories,
      @NonNull TextView textFoodName, @NonNull TextView textServingSize,
      @NonNull TextView textTime) {
    this.rootView = rootView;
    this.textCalories = textCalories;
    this.textFoodName = textFoodName;
    this.textServingSize = textServingSize;
    this.textTime = textTime;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFoodEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFoodEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_food_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFoodEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.text_calories;
      TextView textCalories = ViewBindings.findChildViewById(rootView, id);
      if (textCalories == null) {
        break missingId;
      }

      id = R.id.text_food_name;
      TextView textFoodName = ViewBindings.findChildViewById(rootView, id);
      if (textFoodName == null) {
        break missingId;
      }

      id = R.id.text_serving_size;
      TextView textServingSize = ViewBindings.findChildViewById(rootView, id);
      if (textServingSize == null) {
        break missingId;
      }

      id = R.id.text_time;
      TextView textTime = ViewBindings.findChildViewById(rootView, id);
      if (textTime == null) {
        break missingId;
      }

      return new ItemFoodEntryBinding((MaterialCardView) rootView, textCalories, textFoodName,
          textServingSize, textTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
