/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.calorietracker.app;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String APPLICATION_ID = "com.calorietracker.app";
  public static final String BUILD_TYPE = "debug";
  public static final int VERSION_CODE = 1;
  public static final String VERSION_NAME = "1.0";
  // Field from default config.
  public static final String GEMINI_API_BASE_URL = "https://generativelanguage.googleapis.com/";
  // Field from default config.
  public static final String GEMINI_API_KEY = "AIzaSyAs0vPoI2hA-oBKiLYYC2qVnKSZSzAR5XE";
}
