package com.calorietracker.app.data.database.dao;

import android.database.Cursor;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.calorietracker.app.data.database.entities.Nutrient;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class NutrientDao_Impl implements NutrientDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Nutrient> __insertionAdapterOfNutrient;

  private final EntityDeletionOrUpdateAdapter<Nutrient> __deletionAdapterOfNutrient;

  private final EntityDeletionOrUpdateAdapter<Nutrient> __updateAdapterOfNutrient;

  public NutrientDao_Impl(RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfNutrient = new EntityInsertionAdapter<Nutrient>(__db) {
      @Override
      public String createQuery() {
        return "INSERT OR REPLACE INTO `nutrients` (`id`,`foodEntryId`,`name`,`amount`,`unit`) VALUES (nullif(?, 0),?,?,?,?)";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, Nutrient value) {
        stmt.bindLong(1, value.getId());
        stmt.bindLong(2, value.getFoodEntryId());
        if (value.getName() == null) {
          stmt.bindNull(3);
        } else {
          stmt.bindString(3, value.getName());
        }
        stmt.bindDouble(4, value.getAmount());
        if (value.getUnit() == null) {
          stmt.bindNull(5);
        } else {
          stmt.bindString(5, value.getUnit());
        }
      }
    };
    this.__deletionAdapterOfNutrient = new EntityDeletionOrUpdateAdapter<Nutrient>(__db) {
      @Override
      public String createQuery() {
        return "DELETE FROM `nutrients` WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, Nutrient value) {
        stmt.bindLong(1, value.getId());
      }
    };
    this.__updateAdapterOfNutrient = new EntityDeletionOrUpdateAdapter<Nutrient>(__db) {
      @Override
      public String createQuery() {
        return "UPDATE OR ABORT `nutrients` SET `id` = ?,`foodEntryId` = ?,`name` = ?,`amount` = ?,`unit` = ? WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, Nutrient value) {
        stmt.bindLong(1, value.getId());
        stmt.bindLong(2, value.getFoodEntryId());
        if (value.getName() == null) {
          stmt.bindNull(3);
        } else {
          stmt.bindString(3, value.getName());
        }
        stmt.bindDouble(4, value.getAmount());
        if (value.getUnit() == null) {
          stmt.bindNull(5);
        } else {
          stmt.bindString(5, value.getUnit());
        }
        stmt.bindLong(6, value.getId());
      }
    };
  }

  @Override
  public Object insertAll(final List<Nutrient> nutrients,
      final Continuation<? super Unit> $completion) {
    __db.assertNotSuspendingTransaction();
  }

  @Override
  public Object insert(final Nutrient nutrient, final Continuation<? super Long> $completion) {
    __db.assertNotSuspendingTransaction();
  }

  @Override
  public Object delete(final Nutrient nutrient, final Continuation<? super Unit> $completion) {
    __db.assertNotSuspendingTransaction();
  }

  @Override
  public Object update(final Nutrient nutrient, final Continuation<? super Unit> $completion) {
    __db.assertNotSuspendingTransaction();
  }

  @Override
  public Object getNutrientsForFoodEntry(final long foodEntryId,
      final Continuation<? super List<Nutrient>> $completion) {
    final String _sql = "SELECT * FROM nutrients WHERE foodEntryId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, foodEntryId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final Object _result;
      if(_cursor.moveToFirst()) {
        _result = new Object();
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public Flow<List<Nutrient>> getNutrientsForFoodEntryFlow(final long foodEntryId) {
    final String _sql = "SELECT * FROM nutrients WHERE foodEntryId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, foodEntryId);
    return CoroutinesRoom.createFlow(__db, false, new String[]{"nutrients"}, new Callable<List<Nutrient>>() {
      @Override
      public List<Nutrient> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFoodEntryId = CursorUtil.getColumnIndexOrThrow(_cursor, "foodEntryId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "unit");
          final List<Nutrient> _result = new ArrayList<Nutrient>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final Nutrient _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpFoodEntryId;
            _tmpFoodEntryId = _cursor.getLong(_cursorIndexOfFoodEntryId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpUnit;
            if (_cursor.isNull(_cursorIndexOfUnit)) {
              _tmpUnit = null;
            } else {
              _tmpUnit = _cursor.getString(_cursorIndexOfUnit);
            }
            _item = new Nutrient(_tmpId,_tmpFoodEntryId,_tmpName,_tmpAmount,_tmpUnit);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getTotalNutrientForDate(final String nutrientName, final long date,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(amount) FROM nutrients WHERE name = ? AND foodEntryId IN (SELECT id FROM food_entries WHERE DATE(timestamp/1000, 'unixepoch') = DATE(?/1000, 'unixepoch'))";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (nutrientName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, nutrientName);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, date);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final Object _result;
      if(_cursor.moveToFirst()) {
        final int _tmp;
        _tmp = _cursor.getInt(0);
        _result = _tmp != 0;
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public Object deleteByFoodEntryId(final long foodEntryId,
      final Continuation<? super Unit> $completion) {
    __db.assertNotSuspendingTransaction();
  }

  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
