<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_food_entry" modulePackage="com.calorietracker.app" filePath="app\src\main\res\layout\item_food_entry.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_food_entry_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="51"/></Target><Target id="@+id/text_food_name" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="33" endOffset="53"/></Target><Target id="@+id/text_serving_size" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="42" endOffset="36"/></Target><Target id="@+id/text_calories" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="59" endOffset="39"/></Target><Target id="@+id/text_time" view="TextView"><Expressions/><location startLine="61" startOffset="12" endLine="68" endOffset="36"/></Target></Targets></Layout>