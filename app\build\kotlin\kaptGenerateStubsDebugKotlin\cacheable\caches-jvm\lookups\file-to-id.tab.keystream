Eapp/src/main/java/com/calorietracker/app/CalorieTrackerApplication.ktEapp/src/main/java/com/calorietracker/app/data/api/GeminiApiService.ktIapp/src/main/java/com/calorietracker/app/data/api/models/GeminiRequest.ktJapp/src/main/java/com/calorietracker/app/data/api/models/GeminiResponse.ktEapp/src/main/java/com/calorietracker/app/data/database/AppDatabase.ktJapp/src/main/java/com/calorietracker/app/data/database/dao/FoodEntryDao.ktIapp/src/main/java/com/calorietracker/app/data/database/dao/NutrientDao.ktLapp/src/main/java/com/calorietracker/app/data/database/entities/FoodEntry.ktKapp/src/main/java/com/calorietracker/app/data/database/entities/Nutrient.ktMapp/src/main/java/com/calorietracker/app/data/repository/CalorieRepository.kt=app/src/main/java/com/calorietracker/app/di/DatabaseModule.kt<app/src/main/java/com/calorietracker/app/di/NetworkModule.kt;app/src/main/java/com/calorietracker/app/ui/MainActivity.ktDapp/src/main/java/com/calorietracker/app/ui/camera/CameraActivity.ktEapp/src/main/java/com/calorietracker/app/ui/camera/CameraViewModel.ktKapp/src/main/java/com/calorietracker/app/ui/foodentry/EditFoodItemDialog.ktJapp/src/main/java/com/calorietracker/app/ui/foodentry/FoodEntryActivity.ktKapp/src/main/java/com/calorietracker/app/ui/foodentry/FoodEntryViewModel.ktHapp/src/main/java/com/calorietracker/app/ui/foodentry/FoodItemAdapter.ktDapp/src/main/java/com/calorietracker/app/ui/home/<USER>/src/main/java/com/calorietracker/app/ui/home/<USER>