package com.calorietracker.app.ui.foodentry

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.calorietracker.app.databinding.ItemEditableFoodBinding

class FoodItemAdapter(
    private val onItemClick: (EditableFoodItem) -> Unit
) : ListAdapter<EditableFoodItem, FoodItemAdapter.FoodItemViewHolder>(FoodItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FoodItemViewHolder {
        val binding = ItemEditableFoodBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return FoodItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FoodItemViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class FoodItemViewHolder(
        private val binding: ItemEditableFoodBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(foodItem: EditableFoodItem) {
            binding.apply {
                textFoodName.text = foodItem.name
                textCalories.text = "%.0f kcal".format(foodItem.calories)
                textServingSize.text = "%.0f %s".format(foodItem.servingSize, foodItem.servingUnit)
                
                // Show macronutrient breakdown
                textMacros.text = "P: %.1fg | F: %.1fg | C: %.1fg".format(
                    foodItem.protein,
                    foodItem.fat,
                    foodItem.carbohydrates
                )
                
                root.setOnClickListener {
                    onItemClick(foodItem)
                }
                
                buttonEdit.setOnClickListener {
                    onItemClick(foodItem)
                }
            }
        }
    }
}

class FoodItemDiffCallback : DiffUtil.ItemCallback<EditableFoodItem>() {
    override fun areItemsTheSame(oldItem: EditableFoodItem, newItem: EditableFoodItem): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: EditableFoodItem, newItem: EditableFoodItem): Boolean {
        return oldItem == newItem
    }
}
