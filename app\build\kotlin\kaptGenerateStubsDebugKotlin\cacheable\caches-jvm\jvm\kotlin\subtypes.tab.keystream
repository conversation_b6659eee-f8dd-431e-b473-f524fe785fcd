(androidx.appcompat.app.AppCompatActivityandroid.os.Parcelable(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroidx.lifecycle.ViewModel androidx.viewbinding.ViewBindingandroid.app.Applicationandroidx.room.RoomDatabase>pub.devrel.easypermissions.EasyPermissions.PermissionCallbacks$androidx.fragment.app.DialogFragment                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           