<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_food_entry" modulePackage="com.calorietracker.app" filePath="app\src\main\res\layout\activity_food_entry.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_food_entry_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="174" endOffset="53"/></Target><Target id="@+id/button_back" view="ImageButton"><Expressions/><location startLine="26" startOffset="16" endLine="33" endOffset="53"/></Target><Target id="@+id/image_view_food" view="ImageView"><Expressions/><location startLine="55" startOffset="16" endLine="61" endOffset="60"/></Target><Target id="@+id/text_total_calories" view="TextView"><Expressions/><location startLine="89" startOffset="20" endLine="96" endOffset="66"/></Target><Target id="@+id/recycler_view_food_items" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="125" startOffset="16" endLine="130" endOffset="65"/></Target><Target id="@+id/button_cancel" view="Button"><Expressions/><location startLine="143" startOffset="16" endLine="150" endOffset="43"/></Target><Target id="@+id/button_save" view="Button"><Expressions/><location startLine="152" startOffset="16" endLine="158" endOffset="47"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="167" startOffset="4" endLine="172" endOffset="35"/></Target></Targets></Layout>