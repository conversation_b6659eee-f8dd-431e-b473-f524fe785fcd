package com.calorietracker.app.ui.camera;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = CameraActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface CameraActivity_GeneratedInjector {
  void injectCameraActivity(CameraActivity cameraActivity);
}
