package com.calorietracker.app.data.api

import com.calorietracker.app.data.api.models.GeminiRequest
import com.calorietracker.app.data.api.models.GeminiResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

interface GeminiApiService {
    
    @POST("v1beta/models/gemini-pro-vision:generateContent")
    suspend fun analyzeFoodImage(
        @Query("key") apiKey: String,
        @Body request: GeminiRequest
    ): Response<GeminiResponse>
}
