package com.calorietracker.app.data.api.models

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class GeminiResponse(
    @SerializedName("candidates")
    val candidates: List<Candidate>
)

data class Candidate(
    @SerializedName("content")
    val content: ResponseContent,
    @SerializedName("finishReason")
    val finishReason: String
)

data class ResponseContent(
    @SerializedName("parts")
    val parts: List<ResponsePart>,
    @SerializedName("role")
    val role: String
)

data class ResponsePart(
    @SerializedName("text")
    val text: String
)

// Models for parsed nutritional data
@Parcelize
data class FoodAnalysisResult(
    @SerializedName("foods")
    val foods: List<FoodItem>
) : Parcelable

@Parcelize
data class FoodItem(
    @SerializedName("name")
    val name: String,
    @SerializedName("serving_size")
    val servingSize: Double,
    @SerializedName("serving_unit")
    val servingUnit: String = "g",
    @SerializedName("calories")
    val calories: Double,
    @SerializedName("protein")
    val protein: Double,
    @SerializedName("fat")
    val fat: Double,
    @SerializedName("carbohydrates")
    val carbohydrates: Double,
    @SerializedName("fiber")
    val fiber: Double? = null,
    @SerializedName("sugar")
    val sugar: Double? = null,
    @SerializedName("sodium")
    val sodium: Double? = null
) : Parcelable
