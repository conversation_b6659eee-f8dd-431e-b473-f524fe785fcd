// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.calorietracker.app.di;

import com.calorietracker.app.data.database.AppDatabase;
import com.calorietracker.app.data.database.dao.FoodEntryDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideFoodEntryDaoFactory implements Factory<FoodEntryDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideFoodEntryDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public FoodEntryDao get() {
    return provideFoodEntryDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideFoodEntryDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideFoodEntryDaoFactory(databaseProvider);
  }

  public static FoodEntryDao provideFoodEntryDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideFoodEntryDao(database));
  }
}
