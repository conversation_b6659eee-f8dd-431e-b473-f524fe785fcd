-- Merging decision tree log ---
manifest
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:2:1-53:12
MERGED from [androidx.databinding:databinding-adapters:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf5b497926028e4ce091999739babacc\transformed\databinding-adapters-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b056a2921c25f2f9aea41df56862b0e\transformed\databinding-ktx-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9591cc55f85555c258ae08835b62b69\transformed\databinding-runtime-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b2a0ce122246837d61373c1bdb2ce81e\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\88cc6ed9e97aac313cd5fcec17ca162d\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5d2aba2e4854763de3c830746672350\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616bd8e40f999e98a3bc3f1a38c7240\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e147348306e8f28e08177db3e05ee4f8\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c84cbe45b0631238bdc6e2bd625903f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d99686fe230002bf522721ff96f6168\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b5c4d83df9d6a3e91f7729517accc56\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\eafeb19338603240cae4e7b1190695f6\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1ab3223aae3ecb28712572fda900618\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c4137e29499ac07a49fe7248d23f927\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b755096da34ef058cbb89ab52df62b8\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\745fe4b9e90f40666082dbdce3db2715\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5489ce6f9476cdafd523d00d360839\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1244f984846ed2c3e8f481aa5e4e4246\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:2:1-19:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1681de8487692e35e1956942be5cd238\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1281d8c2183bb3f24cf59a0bdb8ca6de\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d2ec52cfacf8c357f82e8411b1fcc2\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb9c89e09fadaabbb10559b5fd0216cb\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\466421f2de203c641c038727708c0f7f\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\24b253ae245635277285bbd5d59d4fce\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd42cf66f0d519a663633ad32c75adf\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b8b28c1cd454cc2361db0ac6700bf87\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c1404d9d5a7fd75e2289cf552af56c14\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\978c748e9f15b8f0b53e9da27a032438\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e46bd510ec7e19155234cbf05655f63\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6407a68d9bae5f1589a550326941757\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b30e1e33012545f9539e58a267498ed\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f24a6ba1dc60af29a28c4c091792348\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d200bea3bc594f1cf5ea90b76903cd87\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7106064445b9e40933f732ce51b6c8b0\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9ba79f290b0839b221ef7561cabb89\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16416bf374e43e72e434d1181e2ef428\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7634a4de7c9a01fb52aa14a070d6804d\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55be9e261302fe64655634ce8b11f2f6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\014432e344351e39ae33057e99632ccf\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\84f61cbaeb80a730940e8599373fb197\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e889bf4cb892dad738b94bfd8a15be1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\02eb861efcb77be7e35634291b3627f4\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdd1d5d41e2b6432191aa64161ffbe85\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16d391a814ddbe20faed541231ff3fb2\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3cb4b7968ede4bdb5e01712d2c2019f\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85230cc838d5f14b0d40c8c3518bad17\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e15c7ce0cf5add10cd66fba13278f6b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb4394e718d8b99dc98188d2340ce05\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f4905bca8f49ba4acf51c68d53bb1206\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e340c106c659da85f4b8d5d815c3dbab\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\553aaf97c156ba02c3bca375ec7c0d40\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5889911bdf9c967691d19afc059b51a2\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ff6ff1a63ef9b201f456829dbf80d4d\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7d061c31e87d1352e6df4aef336ff17\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de0200e66c4ec7a29904028174198fe7\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d044ad2f292886b703277f00bb1f00ee\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ae32e0d4c314da52cfdc093db2c6c09\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d32c9dff1467a6eadc103486b5eaa7d\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\73729d780dde46caa87761f4d7273ed4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a3282a5c403e03f4fbb521f70a96d2e\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cac14071e4ee993308dffc64458ad760\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\251b47861d0e68f01465692867e10396\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\21fc1606f3e442a1230bc7c3b36a8e3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8df72da10f17eba22138a625d0c7b6dd\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aded28a45f4fed6f54c74b03675fa36c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\004b55280007ef40c75cd31c50745e45\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f1531d54dca196e5905872e3fdc2c97\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97f073b28fa7b1de88d7f292ede5b98a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d584471bc3fcf388cfd38e9e19d2742\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef3966e0c3c2fddcd7c094621efea510\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d931fee8887a58ffbb7c805e4a88608\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.INTERNET
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:7:5-67
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:7:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:9:5-80
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:10:22-78
uses-feature#android.hardware.camera
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:13:5-15:35
	android:required
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:15:9-32
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:14:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:16:5-18:36
	android:required
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:18:9-33
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:17:9-57
application
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:20:5-51:19
INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:20:5-51:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1ab3223aae3ecb28712572fda900618\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1ab3223aae3ecb28712572fda900618\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c4137e29499ac07a49fe7248d23f927\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c4137e29499ac07a49fe7248d23f927\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5489ce6f9476cdafd523d00d360839\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5489ce6f9476cdafd523d00d360839\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:11:5-17:19
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:11:5-17:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\21fc1606f3e442a1230bc7c3b36a8e3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\21fc1606f3e442a1230bc7c3b36a8e3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\004b55280007ef40c75cd31c50745e45\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\004b55280007ef40c75cd31c50745e45\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:28:9-35
	android:label
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:26:9-41
	android:fullBackupContent
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:24:9-54
	android:roundIcon
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:27:9-54
	tools:targetApi
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:31:9-29
	android:icon
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:25:9-43
	android:allowBackup
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:22:9-35
	android:theme
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:29:9-52
	android:dataExtractionRules
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:23:9-65
	android:usesCleartextTraffic
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:30:9-44
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:21:9-50
activity#com.calorietracker.app.ui.MainActivity
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:33:9-41:20
	android:exported
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:36:13-56
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:34:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:37:13-40:29
action#android.intent.action.MAIN
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:38:17-69
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:38:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:39:17-77
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:39:27-74
activity#com.calorietracker.app.ui.camera.CameraActivity
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:43:9-46:52
	android:screenOrientation
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:46:13-49
	android:exported
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:45:13-37
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:44:13-53
activity#com.calorietracker.app.ui.foodentry.FoodEntryActivity
ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:48:9-50:40
	android:exported
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml:49:13-59
uses-sdk
INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf5b497926028e4ce091999739babacc\transformed\databinding-adapters-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf5b497926028e4ce091999739babacc\transformed\databinding-adapters-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b056a2921c25f2f9aea41df56862b0e\transformed\databinding-ktx-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b056a2921c25f2f9aea41df56862b0e\transformed\databinding-ktx-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9591cc55f85555c258ae08835b62b69\transformed\databinding-runtime-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9591cc55f85555c258ae08835b62b69\transformed\databinding-runtime-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b2a0ce122246837d61373c1bdb2ce81e\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b2a0ce122246837d61373c1bdb2ce81e\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\88cc6ed9e97aac313cd5fcec17ca162d\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\88cc6ed9e97aac313cd5fcec17ca162d\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5d2aba2e4854763de3c830746672350\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5d2aba2e4854763de3c830746672350\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616bd8e40f999e98a3bc3f1a38c7240\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616bd8e40f999e98a3bc3f1a38c7240\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e147348306e8f28e08177db3e05ee4f8\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e147348306e8f28e08177db3e05ee4f8\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c84cbe45b0631238bdc6e2bd625903f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c84cbe45b0631238bdc6e2bd625903f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d99686fe230002bf522721ff96f6168\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d99686fe230002bf522721ff96f6168\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b5c4d83df9d6a3e91f7729517accc56\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b5c4d83df9d6a3e91f7729517accc56\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\eafeb19338603240cae4e7b1190695f6\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\eafeb19338603240cae4e7b1190695f6\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1ab3223aae3ecb28712572fda900618\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1ab3223aae3ecb28712572fda900618\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c4137e29499ac07a49fe7248d23f927\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c4137e29499ac07a49fe7248d23f927\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b755096da34ef058cbb89ab52df62b8\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b755096da34ef058cbb89ab52df62b8\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\745fe4b9e90f40666082dbdce3db2715\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\745fe4b9e90f40666082dbdce3db2715\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5489ce6f9476cdafd523d00d360839\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5489ce6f9476cdafd523d00d360839\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1244f984846ed2c3e8f481aa5e4e4246\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1244f984846ed2c3e8f481aa5e4e4246\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1681de8487692e35e1956942be5cd238\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1681de8487692e35e1956942be5cd238\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1281d8c2183bb3f24cf59a0bdb8ca6de\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1281d8c2183bb3f24cf59a0bdb8ca6de\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d2ec52cfacf8c357f82e8411b1fcc2\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\49d2ec52cfacf8c357f82e8411b1fcc2\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb9c89e09fadaabbb10559b5fd0216cb\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb9c89e09fadaabbb10559b5fd0216cb\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\466421f2de203c641c038727708c0f7f\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\466421f2de203c641c038727708c0f7f\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\24b253ae245635277285bbd5d59d4fce\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\24b253ae245635277285bbd5d59d4fce\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd42cf66f0d519a663633ad32c75adf\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7dd42cf66f0d519a663633ad32c75adf\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b8b28c1cd454cc2361db0ac6700bf87\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b8b28c1cd454cc2361db0ac6700bf87\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c1404d9d5a7fd75e2289cf552af56c14\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c1404d9d5a7fd75e2289cf552af56c14\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\978c748e9f15b8f0b53e9da27a032438\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\978c748e9f15b8f0b53e9da27a032438\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e46bd510ec7e19155234cbf05655f63\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e46bd510ec7e19155234cbf05655f63\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6407a68d9bae5f1589a550326941757\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6407a68d9bae5f1589a550326941757\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b30e1e33012545f9539e58a267498ed\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b30e1e33012545f9539e58a267498ed\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f24a6ba1dc60af29a28c4c091792348\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f24a6ba1dc60af29a28c4c091792348\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d200bea3bc594f1cf5ea90b76903cd87\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d200bea3bc594f1cf5ea90b76903cd87\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7106064445b9e40933f732ce51b6c8b0\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7106064445b9e40933f732ce51b6c8b0\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9ba79f290b0839b221ef7561cabb89\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9ba79f290b0839b221ef7561cabb89\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16416bf374e43e72e434d1181e2ef428\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16416bf374e43e72e434d1181e2ef428\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7634a4de7c9a01fb52aa14a070d6804d\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7634a4de7c9a01fb52aa14a070d6804d\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55be9e261302fe64655634ce8b11f2f6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\55be9e261302fe64655634ce8b11f2f6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\014432e344351e39ae33057e99632ccf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\014432e344351e39ae33057e99632ccf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\84f61cbaeb80a730940e8599373fb197\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\84f61cbaeb80a730940e8599373fb197\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e889bf4cb892dad738b94bfd8a15be1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e889bf4cb892dad738b94bfd8a15be1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\02eb861efcb77be7e35634291b3627f4\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\02eb861efcb77be7e35634291b3627f4\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdd1d5d41e2b6432191aa64161ffbe85\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bdd1d5d41e2b6432191aa64161ffbe85\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16d391a814ddbe20faed541231ff3fb2\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16d391a814ddbe20faed541231ff3fb2\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3cb4b7968ede4bdb5e01712d2c2019f\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3cb4b7968ede4bdb5e01712d2c2019f\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85230cc838d5f14b0d40c8c3518bad17\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85230cc838d5f14b0d40c8c3518bad17\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e15c7ce0cf5add10cd66fba13278f6b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e15c7ce0cf5add10cd66fba13278f6b\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb4394e718d8b99dc98188d2340ce05\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebb4394e718d8b99dc98188d2340ce05\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f4905bca8f49ba4acf51c68d53bb1206\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f4905bca8f49ba4acf51c68d53bb1206\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e340c106c659da85f4b8d5d815c3dbab\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e340c106c659da85f4b8d5d815c3dbab\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\553aaf97c156ba02c3bca375ec7c0d40\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\553aaf97c156ba02c3bca375ec7c0d40\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5889911bdf9c967691d19afc059b51a2\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5889911bdf9c967691d19afc059b51a2\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ff6ff1a63ef9b201f456829dbf80d4d\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ff6ff1a63ef9b201f456829dbf80d4d\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7d061c31e87d1352e6df4aef336ff17\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7d061c31e87d1352e6df4aef336ff17\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de0200e66c4ec7a29904028174198fe7\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de0200e66c4ec7a29904028174198fe7\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d044ad2f292886b703277f00bb1f00ee\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d044ad2f292886b703277f00bb1f00ee\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ae32e0d4c314da52cfdc093db2c6c09\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ae32e0d4c314da52cfdc093db2c6c09\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d32c9dff1467a6eadc103486b5eaa7d\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d32c9dff1467a6eadc103486b5eaa7d\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\73729d780dde46caa87761f4d7273ed4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\73729d780dde46caa87761f4d7273ed4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a3282a5c403e03f4fbb521f70a96d2e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a3282a5c403e03f4fbb521f70a96d2e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cac14071e4ee993308dffc64458ad760\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cac14071e4ee993308dffc64458ad760\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\251b47861d0e68f01465692867e10396\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\251b47861d0e68f01465692867e10396\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\21fc1606f3e442a1230bc7c3b36a8e3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\21fc1606f3e442a1230bc7c3b36a8e3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8df72da10f17eba22138a625d0c7b6dd\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8df72da10f17eba22138a625d0c7b6dd\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aded28a45f4fed6f54c74b03675fa36c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aded28a45f4fed6f54c74b03675fa36c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\004b55280007ef40c75cd31c50745e45\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\004b55280007ef40c75cd31c50745e45\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f1531d54dca196e5905872e3fdc2c97\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f1531d54dca196e5905872e3fdc2c97\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97f073b28fa7b1de88d7f292ede5b98a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97f073b28fa7b1de88d7f292ede5b98a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d584471bc3fcf388cfd38e9e19d2742\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d584471bc3fcf388cfd38e9e19d2742\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef3966e0c3c2fddcd7c094621efea510\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef3966e0c3c2fddcd7c094621efea510\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d931fee8887a58ffbb7c805e4a88608\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\2d931fee8887a58ffbb7c805e4a88608\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\CalorieTrackerApp\CalorieTrackerApp\app\src\main\AndroidManifest.xml
queries
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ebac996856a9713dbb2bcd0ce9534ca\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5489ce6f9476cdafd523d00d360839\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d5489ce6f9476cdafd523d00d360839\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d8dbe6cbac2bb9cc9821c5c7c17cfdf\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
activity#pub.devrel.easypermissions.AppSettingsDialogHolderActivity
ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:12:9-16:66
	android:label
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:15:13-29
	android:exported
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:16:13-63
	android:name
		ADDED from [pub.devrel:easypermissions:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7483bce50e105d800a1d1c231977f2ed\transformed\easypermissions-3.0.0\AndroidManifest.xml:13:13-86
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\21fc1606f3e442a1230bc7c3b36a8e3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\21fc1606f3e442a1230bc7c3b36a8e3a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\797768e5bef8cf7d458faceaf2553e72\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8beb10ebcb3f77913e4823d80176857\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.calorietracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.calorietracker.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8842954a4c09a5d828669ce7e1d2598c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54aab235deb67f0ddac795e8d317234c\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a5acd2ceff4427695c50fbe0a918a2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d99aa60e9240ca4a03ff114c22452f29\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
