// Generated by view binder compiler. Do not edit!
package com.calorietracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.calorietracker.app.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton fabAddFood;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ProgressBar progressCalories;

  @NonNull
  public final RecyclerView recyclerViewFoodEntries;

  @NonNull
  public final TextView textTotalCalories;

  @NonNull
  public final TextView textTotalCarbs;

  @NonNull
  public final TextView textTotalFat;

  @NonNull
  public final TextView textTotalProtein;

  private ActivityMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton fabAddFood, @NonNull ProgressBar progressBar,
      @NonNull ProgressBar progressCalories, @NonNull RecyclerView recyclerViewFoodEntries,
      @NonNull TextView textTotalCalories, @NonNull TextView textTotalCarbs,
      @NonNull TextView textTotalFat, @NonNull TextView textTotalProtein) {
    this.rootView = rootView;
    this.fabAddFood = fabAddFood;
    this.progressBar = progressBar;
    this.progressCalories = progressCalories;
    this.recyclerViewFoodEntries = recyclerViewFoodEntries;
    this.textTotalCalories = textTotalCalories;
    this.textTotalCarbs = textTotalCarbs;
    this.textTotalFat = textTotalFat;
    this.textTotalProtein = textTotalProtein;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fab_add_food;
      FloatingActionButton fabAddFood = ViewBindings.findChildViewById(rootView, id);
      if (fabAddFood == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progress_calories;
      ProgressBar progressCalories = ViewBindings.findChildViewById(rootView, id);
      if (progressCalories == null) {
        break missingId;
      }

      id = R.id.recycler_view_food_entries;
      RecyclerView recyclerViewFoodEntries = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewFoodEntries == null) {
        break missingId;
      }

      id = R.id.text_total_calories;
      TextView textTotalCalories = ViewBindings.findChildViewById(rootView, id);
      if (textTotalCalories == null) {
        break missingId;
      }

      id = R.id.text_total_carbs;
      TextView textTotalCarbs = ViewBindings.findChildViewById(rootView, id);
      if (textTotalCarbs == null) {
        break missingId;
      }

      id = R.id.text_total_fat;
      TextView textTotalFat = ViewBindings.findChildViewById(rootView, id);
      if (textTotalFat == null) {
        break missingId;
      }

      id = R.id.text_total_protein;
      TextView textTotalProtein = ViewBindings.findChildViewById(rootView, id);
      if (textTotalProtein == null) {
        break missingId;
      }

      return new ActivityMainBinding((CoordinatorLayout) rootView, fabAddFood, progressBar,
          progressCalories, recyclerViewFoodEntries, textTotalCalories, textTotalCarbs,
          textTotalFat, textTotalProtein);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
