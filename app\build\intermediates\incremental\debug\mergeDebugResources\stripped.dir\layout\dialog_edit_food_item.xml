<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Food Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="Food Name"
            app:startIconDrawable="@drawable/ic_edit">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_food_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textCapWords" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Serving Size Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:layout_marginEnd="8dp"
                android:hint="Serving Size">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_serving_size"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="Unit">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_serving_unit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Calories -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="Calories (kcal)">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_calories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Macronutrients Header -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Macronutrients"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp" />

        <!-- Protein, Fat, Carbs Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:hint="Protein (g)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_protein"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:hint="Fat (g)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_fat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:hint="Carbs (g)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_carbohydrates"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Optional Nutrients Header -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Additional Nutrients (Optional)"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp" />

        <!-- Fiber and Sugar Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="Fiber (g)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_fiber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="Sugar (g)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_sugar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Sodium -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="Sodium (mg)">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_sodium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal" />

        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
