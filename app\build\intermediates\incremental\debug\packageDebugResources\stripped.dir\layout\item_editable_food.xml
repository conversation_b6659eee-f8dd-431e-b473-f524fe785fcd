<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_food_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    tools:text="Grilled Chicken Breast" />

                <TextView
                    android:id="@+id/text_serving_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray"
                    android:layout_marginTop="2dp"
                    tools:text="150 g" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/text_calories"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:layout_marginEnd="12dp"
                    tools:text="165 kcal" />

                <ImageButton
                    android:id="@+id/button_edit"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_edit"
                    android:contentDescription="Edit"
                    app:tint="@color/primary_color" />

            </LinearLayout>

        </LinearLayout>

        <!-- Macronutrient breakdown -->
        <TextView
            android:id="@+id/text_macros"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="8dp"
            tools:text="P: 31.0g | F: 3.6g | C: 0.0g" />

        <!-- Progress bars for macronutrients (optional visual enhancement) -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:weightSum="3">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Protein"
                    android:textSize="10sp"
                    android:textColor="@android:color/darker_gray" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:background="@color/protein_color"
                    android:alpha="0.3"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginHorizontal="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fat"
                    android:textSize="10sp"
                    android:textColor="@android:color/darker_gray" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:background="@color/fat_color"
                    android:alpha="0.3"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Carbs"
                    android:textSize="10sp"
                    android:textColor="@android:color/darker_gray" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:background="@color/carbs_color"
                    android:alpha="0.3"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
