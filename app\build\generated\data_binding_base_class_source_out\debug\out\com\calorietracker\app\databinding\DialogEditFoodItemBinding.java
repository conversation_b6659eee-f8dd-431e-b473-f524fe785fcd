// Generated by view binder compiler. Do not edit!
package com.calorietracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.calorietracker.app.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogEditFoodItemBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final TextInputEditText editTextCalories;

  @NonNull
  public final TextInputEditText editTextCarbohydrates;

  @NonNull
  public final TextInputEditText editTextFat;

  @NonNull
  public final TextInputEditText editTextFiber;

  @NonNull
  public final TextInputEditText editTextFoodName;

  @NonNull
  public final TextInputEditText editTextProtein;

  @NonNull
  public final TextInputEditText editTextServingSize;

  @NonNull
  public final TextInputEditText editTextServingUnit;

  @NonNull
  public final TextInputEditText editTextSodium;

  @NonNull
  public final TextInputEditText editTextSugar;

  private DialogEditFoodItemBinding(@NonNull NestedScrollView rootView,
      @NonNull TextInputEditText editTextCalories, @NonNull TextInputEditText editTextCarbohydrates,
      @NonNull TextInputEditText editTextFat, @NonNull TextInputEditText editTextFiber,
      @NonNull TextInputEditText editTextFoodName, @NonNull TextInputEditText editTextProtein,
      @NonNull TextInputEditText editTextServingSize,
      @NonNull TextInputEditText editTextServingUnit, @NonNull TextInputEditText editTextSodium,
      @NonNull TextInputEditText editTextSugar) {
    this.rootView = rootView;
    this.editTextCalories = editTextCalories;
    this.editTextCarbohydrates = editTextCarbohydrates;
    this.editTextFat = editTextFat;
    this.editTextFiber = editTextFiber;
    this.editTextFoodName = editTextFoodName;
    this.editTextProtein = editTextProtein;
    this.editTextServingSize = editTextServingSize;
    this.editTextServingUnit = editTextServingUnit;
    this.editTextSodium = editTextSodium;
    this.editTextSugar = editTextSugar;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogEditFoodItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogEditFoodItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_edit_food_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogEditFoodItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.edit_text_calories;
      TextInputEditText editTextCalories = ViewBindings.findChildViewById(rootView, id);
      if (editTextCalories == null) {
        break missingId;
      }

      id = R.id.edit_text_carbohydrates;
      TextInputEditText editTextCarbohydrates = ViewBindings.findChildViewById(rootView, id);
      if (editTextCarbohydrates == null) {
        break missingId;
      }

      id = R.id.edit_text_fat;
      TextInputEditText editTextFat = ViewBindings.findChildViewById(rootView, id);
      if (editTextFat == null) {
        break missingId;
      }

      id = R.id.edit_text_fiber;
      TextInputEditText editTextFiber = ViewBindings.findChildViewById(rootView, id);
      if (editTextFiber == null) {
        break missingId;
      }

      id = R.id.edit_text_food_name;
      TextInputEditText editTextFoodName = ViewBindings.findChildViewById(rootView, id);
      if (editTextFoodName == null) {
        break missingId;
      }

      id = R.id.edit_text_protein;
      TextInputEditText editTextProtein = ViewBindings.findChildViewById(rootView, id);
      if (editTextProtein == null) {
        break missingId;
      }

      id = R.id.edit_text_serving_size;
      TextInputEditText editTextServingSize = ViewBindings.findChildViewById(rootView, id);
      if (editTextServingSize == null) {
        break missingId;
      }

      id = R.id.edit_text_serving_unit;
      TextInputEditText editTextServingUnit = ViewBindings.findChildViewById(rootView, id);
      if (editTextServingUnit == null) {
        break missingId;
      }

      id = R.id.edit_text_sodium;
      TextInputEditText editTextSodium = ViewBindings.findChildViewById(rootView, id);
      if (editTextSodium == null) {
        break missingId;
      }

      id = R.id.edit_text_sugar;
      TextInputEditText editTextSugar = ViewBindings.findChildViewById(rootView, id);
      if (editTextSugar == null) {
        break missingId;
      }

      return new DialogEditFoodItemBinding((NestedScrollView) rootView, editTextCalories,
          editTextCarbohydrates, editTextFat, editTextFiber, editTextFoodName, editTextProtein,
          editTextServingSize, editTextServingUnit, editTextSodium, editTextSugar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
