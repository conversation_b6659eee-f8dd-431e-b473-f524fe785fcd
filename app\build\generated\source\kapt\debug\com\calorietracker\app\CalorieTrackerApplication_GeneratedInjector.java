package com.calorietracker.app;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = CalorieTrackerApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface CalorieTrackerApplication_GeneratedInjector {
  void injectCalorieTrackerApplication(CalorieTrackerApplication calorieTrackerApplication);
}
