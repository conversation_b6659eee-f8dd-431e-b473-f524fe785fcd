[{"merged": "com.calorietracker.app-mergeDebugResources-62:/layout/activity_camera.xml", "source": "com.calorietracker.app-main-65:/layout/activity_camera.xml"}, {"merged": "com.calorietracker.app-mergeDebugResources-62:/layout/dialog_edit_food_item.xml", "source": "com.calorietracker.app-main-65:/layout/dialog_edit_food_item.xml"}, {"merged": "com.calorietracker.app-mergeDebugResources-62:/layout/item_editable_food.xml", "source": "com.calorietracker.app-main-65:/layout/item_editable_food.xml"}, {"merged": "com.calorietracker.app-mergeDebugResources-62:/layout/activity_food_entry.xml", "source": "com.calorietracker.app-main-65:/layout/activity_food_entry.xml"}, {"merged": "com.calorietracker.app-mergeDebugResources-62:/layout/activity_main.xml", "source": "com.calorietracker.app-main-65:/layout/activity_main.xml"}, {"merged": "com.calorietracker.app-mergeDebugResources-62:/layout/item_food_entry.xml", "source": "com.calorietracker.app-main-65:/layout/item_food_entry.xml"}]