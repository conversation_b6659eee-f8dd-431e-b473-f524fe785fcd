0com/calorietracker/app/CalorieTrackerApplication0com/calorietracker/app/data/api/GeminiApiService4com/calorietracker/app/data/api/models/GeminiRequest.com/calorietracker/app/data/api/models/Content+com/calorietracker/app/data/api/models/Part1com/calorietracker/app/data/api/models/InlineData5com/calorietracker/app/data/api/models/GeminiResponse0com/calorietracker/app/data/api/models/Candidate6com/calorietracker/app/data/api/models/ResponseContent3com/calorietracker/app/data/api/models/ResponsePart9com/calorietracker/app/data/api/models/FoodAnalysisResult/com/calorietracker/app/data/api/models/FoodItem0com/calorietracker/app/data/database/AppDatabase:com/calorietracker/app/data/database/AppDatabase$Companion5com/calorietracker/app/data/database/dao/FoodEntryDao4com/calorietracker/app/data/database/dao/NutrientDao7com/calorietracker/app/data/database/entities/FoodEntry6com/calorietracker/app/data/database/entities/Nutrient8com/calorietracker/app/data/repository/CalorieRepository(com/calorietracker/app/di/DatabaseModule'com/calorietracker/app/di/NetworkModule&com/calorietracker/app/ui/MainActivity/com/calorietracker/app/ui/camera/CameraActivity9com/calorietracker/app/ui/camera/CameraActivity$Companion0com/calorietracker/app/ui/camera/CameraViewModel<com/calorietracker/app/ui/camera/FoodAnalysisResultWithImage6com/calorietracker/app/ui/foodentry/EditFoodItemDialog@com/calorietracker/app/ui/foodentry/EditFoodItemDialog$Companion5com/calorietracker/app/ui/foodentry/FoodEntryActivity6com/calorietracker/app/ui/foodentry/FoodEntryViewModel4com/calorietracker/app/ui/foodentry/EditableFoodItem3com/calorietracker/app/ui/foodentry/FoodItemAdapterFcom/calorietracker/app/ui/foodentry/FoodItemAdapter$FoodItemViewHolder8com/calorietracker/app/ui/foodentry/FoodItemDiffCallback/com/calorietracker/app/ui/home/<USER>/calorietracker/app/ui/home/<USER>/calorietracker/app/ui/home/<USER>/calorietracker/app/ui/home/<USER>/calorietracker/app/ui/home/<USER>