// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = "1.9.22"
    ext.hilt_version = "2.48"
    ext.room_version = "2.5.0"
    ext.retrofit_version = "2.9.0"
    ext.camerax_version = "1.3.1"
    
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:8.2.0"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
    }
}



task clean(type: Delete) {
    delete rootProject.buildDir
}
