0com.calorietracker.app.CalorieTrackerApplication0com.calorietracker.app.data.api.GeminiApiService4com.calorietracker.app.data.api.models.GeminiRequest.com.calorietracker.app.data.api.models.Content+com.calorietracker.app.data.api.models.Part1com.calorietracker.app.data.api.models.InlineData5com.calorietracker.app.data.api.models.GeminiResponse0com.calorietracker.app.data.api.models.Candidate6com.calorietracker.app.data.api.models.ResponseContent3com.calorietracker.app.data.api.models.ResponsePart9com.calorietracker.app.data.api.models.FoodAnalysisResult/com.calorietracker.app.data.api.models.FoodItem0com.calorietracker.app.data.database.AppDatabase:com.calorietracker.app.data.database.AppDatabase.Companion5com.calorietracker.app.data.database.dao.FoodEntryDao4com.calorietracker.app.data.database.dao.NutrientDao7com.calorietracker.app.data.database.entities.FoodEntry6com.calorietracker.app.data.database.entities.Nutrient8com.calorietracker.app.data.repository.CalorieRepository(com.calorietracker.app.di.DatabaseModule'com.calorietracker.app.di.NetworkModule&com.calorietracker.app.ui.MainActivity/com.calorietracker.app.ui.camera.CameraActivity9com.calorietracker.app.ui.camera.CameraActivity.Companion0com.calorietracker.app.ui.camera.CameraViewModel<<EMAIL>/com.calorietracker.app.ui.home.FoodEntryAdapterCcom.calorietracker.app.ui.home.FoodEntryAdapter.FoodEntryViewHolder4com.calorietracker.app.ui.home.FoodEntryDiffCallback,com.calorietracker.app.ui.home.HomeViewModel+com.calorietracker.app.ui.home.DailySummary:com.calorietracker.app.databinding.ItemEditableFoodBinding7com.calorietracker.app.databinding.ItemFoodEntryBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              