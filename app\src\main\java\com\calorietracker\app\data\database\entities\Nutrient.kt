package com.calorietracker.app.data.database.entities

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey

@Entity(
    tableName = "nutrients",
    foreignKeys = [ForeignKey(
        entity = FoodEntry::class,
        parentColumns = ["id"],
        childColumns = ["foodEntryId"],
        onDelete = ForeignKey.CASCADE
    )]
)
data class Nutrient(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val foodEntryId: Long,
    val name: String, // e.g., "calories", "protein", "fat", "carbohydrates"
    val amount: Double,
    val unit: String // e.g., "g", "mg", "kcal"
)
