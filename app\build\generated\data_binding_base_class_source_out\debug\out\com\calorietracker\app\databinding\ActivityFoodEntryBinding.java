// Generated by view binder compiler. Do not edit!
package com.calorietracker.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.calorietracker.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFoodEntryBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton buttonBack;

  @NonNull
  public final Button buttonCancel;

  @NonNull
  public final Button buttonSave;

  @NonNull
  public final ImageView imageViewFood;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewFoodItems;

  @NonNull
  public final TextView textTotalCalories;

  private ActivityFoodEntryBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton buttonBack, @NonNull Button buttonCancel, @NonNull Button buttonSave,
      @NonNull ImageView imageViewFood, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewFoodItems, @NonNull TextView textTotalCalories) {
    this.rootView = rootView;
    this.buttonBack = buttonBack;
    this.buttonCancel = buttonCancel;
    this.buttonSave = buttonSave;
    this.imageViewFood = imageViewFood;
    this.progressBar = progressBar;
    this.recyclerViewFoodItems = recyclerViewFoodItems;
    this.textTotalCalories = textTotalCalories;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFoodEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFoodEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_food_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFoodEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_back;
      ImageButton buttonBack = ViewBindings.findChildViewById(rootView, id);
      if (buttonBack == null) {
        break missingId;
      }

      id = R.id.button_cancel;
      Button buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_save;
      Button buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.image_view_food;
      ImageView imageViewFood = ViewBindings.findChildViewById(rootView, id);
      if (imageViewFood == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view_food_items;
      RecyclerView recyclerViewFoodItems = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewFoodItems == null) {
        break missingId;
      }

      id = R.id.text_total_calories;
      TextView textTotalCalories = ViewBindings.findChildViewById(rootView, id);
      if (textTotalCalories == null) {
        break missingId;
      }

      return new ActivityFoodEntryBinding((CoordinatorLayout) rootView, buttonBack, buttonCancel,
          buttonSave, imageViewFood, progressBar, recyclerViewFoodItems, textTotalCalories);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
