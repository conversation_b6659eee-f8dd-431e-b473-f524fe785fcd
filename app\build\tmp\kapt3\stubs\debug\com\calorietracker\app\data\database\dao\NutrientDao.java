package com.calorietracker.app.data.database.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00050\f2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\f0\u000e2\u0006\u0010\b\u001a\u00020\tH\'J \u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u0016\u0010\u0015\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0016\u001a\u00020\u00032\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00050\fH\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u0019\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006\u001a"}, d2 = {"Lcom/calorietracker/app/data/database/dao/NutrientDao;", "", "delete", "", "nutrient", "Lcom/calorietracker/app/data/database/entities/Nutrient;", "(Lcom/calorietracker/app/data/database/entities/Nutrient;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteByFoodEntryId", "foodEntryId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getNutrientsForFoodEntry", "", "getNutrientsForFoodEntryFlow", "Lkotlinx/coroutines/flow/Flow;", "getTotalNutrientForDate", "", "nutrientName", "", "date", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insert", "insertAll", "nutrients", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "update", "app_debug"})
@androidx.room.Dao()
public abstract interface NutrientDao {
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAll(@org.jetbrains.annotations.NotNull()
    java.util.List<com.calorietracker.app.data.database.entities.Nutrient> nutrients, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insert(@org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.database.entities.Nutrient nutrient, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM nutrients WHERE foodEntryId = :foodEntryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getNutrientsForFoodEntry(long foodEntryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.calorietracker.app.data.database.entities.Nutrient>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM nutrients WHERE foodEntryId = :foodEntryId")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.calorietracker.app.data.database.entities.Nutrient>> getNutrientsForFoodEntryFlow(long foodEntryId);
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM nutrients WHERE name = :nutrientName AND foodEntryId IN (SELECT id FROM food_entries WHERE DATE(timestamp/1000, \'unixepoch\') = DATE(:date/1000, \'unixepoch\'))")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalNutrientForDate(@org.jetbrains.annotations.NotNull()
    java.lang.String nutrientName, long date, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object update(@org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.database.entities.Nutrient nutrient, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object delete(@org.jetbrains.annotations.NotNull()
    com.calorietracker.app.data.database.entities.Nutrient nutrient, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM nutrients WHERE foodEntryId = :foodEntryId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteByFoodEntryId(long foodEntryId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}