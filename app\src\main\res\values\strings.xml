<resources>
    <string name="app_name">Calorie Tracker</string>
    
    <!-- Main Activity -->
    <string name="todays_summary">Today\'s Summary</string>
    <string name="todays_meals">Today\'s Meals</string>
    <string name="add_food">Add Food</string>
    <string name="calories">Calories</string>
    <string name="protein">Protein</string>
    <string name="fat">Fat</string>
    <string name="carbs">Carbs</string>
    <string name="total_calories_format">%d kcal</string>
    <string name="nutrient_format">%.1f g</string>
    
    <!-- Camera Activity -->
    <string name="camera_instructions">Point camera at your food and tap to capture</string>
    <string name="take_photo">Take Photo</string>
    <string name="analyzing_food">Analyzing food...</string>
    <string name="camera_permission_required">Camera permission is required</string>
    <string name="photo_capture_failed">Photo capture failed</string>
    <string name="error_processing_image">Error processing image: %s</string>
    
    <!-- Food Entry Activity -->
    <string name="review_food_entry">Review Food Entry</string>
    <string name="total_calories">Total Calories</string>
    <string name="detected_food_items">Detected Food Items</string>
    <string name="tap_to_edit_instruction">Tap any item to edit its nutritional information</string>
    <string name="cancel">Cancel</string>
    <string name="save_entry">Save Entry</string>
    <string name="food_entry_saved">Food entry saved!</string>
    <string name="no_analysis_result">No analysis result found</string>
    <string name="no_food_items_to_save">No food items to save</string>
    <string name="error_saving_food_entry">Error saving food entry: %s</string>
    
    <!-- Food Analysis -->
    <string name="no_food_detected">No food items detected in the image. Please try again with a clearer photo.</string>
    <string name="analysis_failed">Analysis failed: %s</string>
    <string name="error_analyzing_image">Error analyzing image: %s</string>
    
    <!-- Edit Food Dialog -->
    <string name="edit_food_item">Edit Food Item</string>
    <string name="food_name">Food Name</string>
    <string name="serving_size">Serving Size</string>
    <string name="serving_unit">Unit</string>
    <string name="fiber">Fiber</string>
    <string name="sugar">Sugar</string>
    <string name="sodium">Sodium</string>
    <string name="save">Save</string>
    <string name="invalid_input">Please check your input values</string>
    
    <!-- General -->
    <string name="back">Back</string>
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="loading">Loading...</string>
    <string name="error">Error</string>
    <string name="retry">Retry</string>
    <string name="ok">OK</string>
    
    <!-- Units -->
    <string name="grams">g</string>
    <string name="milligrams">mg</string>
    <string name="kcal">kcal</string>
    
    <!-- Meal types -->
    <string name="breakfast">Breakfast</string>
    <string name="lunch">Lunch</string>
    <string name="dinner">Dinner</string>
    <string name="snack">Snack</string>
    <string name="mixed_meal">Mixed meal (%d items)</string>
</resources>
